import { Center, Spinner, VStack, Image } from "@chakra-ui/react";

export default function LoadingScreen() {
  return (
    <VStack
      flex={1}
      width={"100vw"}
      height={"100vh"}
      justifyContent="center"
      alignItems="center"
    >
      <Center>
        <VStack>
          <Image
            src="/images/logoBancoABC.svg"
            alt="Banco ABC"
            w="100px"
            h="auto"
            filter={
              "brightness(0) saturate(100%) invert(11%) sepia(4%) saturate(0%) hue-rotate(314deg) brightness(96%) contrast(88%)"
            }
          />
          <Spinner size="lg" color="rgb(103, 42, 32)" />
        </VStack>
      </Center>
    </VStack>
  );
}
