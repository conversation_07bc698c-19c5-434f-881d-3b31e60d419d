import { api } from "@/services/api";
import { GetAllQuestionsDto } from "@/utils/types/DTO/questions/questions.dto";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

type TGetAllQuestions = {
  page?: number;
  limit?: number;
  searchTerm?: string;
};

async function getAllQuestions(params: TGetAllQuestions) {
  const { data } = await api.get<GetAllQuestionsDto>(`/management/question`, {
    params: {
      page: params.page,
      limit: params.limit,
      search: params.searchTerm,
    },
  });
  return data;
}

export function useGetAllQuestions(params: TGetAllQuestions) {
  return useQuery({
    queryKey: ["questions", params],
    queryFn: () => getAllQuestions(params),
    placeholderData: keepPreviousData,
  });
}
