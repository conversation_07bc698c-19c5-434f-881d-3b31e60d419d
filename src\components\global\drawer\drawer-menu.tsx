"use client";
// import {
//   DrawerBackdrop,
//   DrawerBody,
//   DrawerCloseTrigger,
//   DrawerContent,
//   DrawerRoot,
//   DrawerTrigger,
// } from "@/components/ui/drawer";
import {
  Box,
  Button,
  CloseButton,
  Drawer,
  HStack,
  IconButton,
  Image,
  Portal,
  Text,
} from "@chakra-ui/react";
import { RxHamburgerMenu } from "react-icons/rx";
import Cookies from "js-cookie";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
// import {
//   MenuContent,
//   MenuItem,
//   MenuRoot,
//   MenuTrigger,
// } from "@/components/ui/menu";

type DrawerMenuProps = {
  children: React.ReactNode;
};

export default function DrawerMenu({ children }: DrawerMenuProps) {
  const [open, setOpen] = useState(false);
  const router = useRouter();

  // const handleLogout = () => {
  //   Cookies.remove("__PlyrChat_Token");
  //   router.push("/");
  // };

  return (
    <Box w={"100%"} position={"absolute"} top={0} zIndex={"overlay"}>
      <HStack w={"100%"}>
        <Drawer.Root
          open={open}
          onOpenChange={(e) => setOpen(e.open)}
          placement={"start"}
        >
          <Drawer.Trigger asChild>
            <IconButton
              aria-label="Go Back"
              bgColor={"transparent"}
              color={"white"}
              size={"2xl"}
              borderWidth={2}
              _active={{
                bgColor: "transparent",
                borderColor: "chatPrimary",
                color: "chatPrimary",
                borderWidth: 2,
              }}
            >
              <RxHamburgerMenu />
            </IconButton>
          </Drawer.Trigger>
          <Portal>
            <Drawer.Positioner>
              <Drawer.Backdrop />
              <Drawer.Content bgColor={"rgb(35,34,34)"}>
                <Drawer.Header>
                  <Drawer.Title>
                    <Image
                      src="/images/logoBancoABC.svg"
                      alt="Banco ABC"
                      w="30px"
                      h="auto"
                    />
                  </Drawer.Title>
                </Drawer.Header>
                <Drawer.Body p={0}>{children}</Drawer.Body>
                <Drawer.CloseTrigger asChild>
                  <CloseButton size="sm" />
                </Drawer.CloseTrigger>
              </Drawer.Content>
            </Drawer.Positioner>
          </Portal>
        </Drawer.Root>
      </HStack>
    </Box>
  );
}
