import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { validateToken } from "./utils/funcs/validateToken";

export async function middleware(request: NextRequest) {
  const cookie = request.cookies.get("__ASSMT_TOKEN")?.value;
  if (!cookie) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  // Validate token before proceeding
  const { isValid, decodedToken, errorMessage } = validateToken(cookie);
  if (!isValid || !decodedToken) {
    console.log(`Middleware token validation failed: ${errorMessage}`);
    const response = NextResponse.redirect(new URL("/", request.url));
    response.cookies.set("__ASSMT_TOKEN", "", {
      path: "/",
      expires: new Date(0),
    });
    return response;
  }

  const { role } = decodedToken;
  const { pathname } = request.nextUrl;

  // Se o usuário for um USER (officer) e tentar acessar rotas de management, redireciona para officer
  if (role === "USER" && pathname.startsWith("/management")) {
    return NextResponse.redirect(new URL("/officer", request.url));
  }

  // Se o usuário for um ADMIN e tentar acessar rotas de officer, redireciona para o management
  if (role === "ADMIN" && pathname.startsWith("/officer")) {
    return NextResponse.redirect(new URL("/management", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/officer/:path*", "/management/:path*"],
};
