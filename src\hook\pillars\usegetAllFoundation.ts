import { api } from "@/services/api";
import { useQuery } from "@tanstack/react-query";
import { useGetAllFoundationDto } from "@/utils/types/DTO/pillars/foundation.dto";

type TGetAllFoundation = {
  searchTerm?: string;
  page?: number;
  limit?: number;
};

async function getAllFoundation(params: TGetAllFoundation) {
  const { data } = await api.get<useGetAllFoundationDto>(`/management/foundations`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllFoundation(params: TGetAllFoundation) {
  return useQuery({
    queryKey: ["foundation", params],
    queryFn: async () => await getAllFoundation(params),
  });
}
