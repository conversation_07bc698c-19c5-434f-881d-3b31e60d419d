import { api } from "@/services/api";
import { GetAllUsersDto } from "@/utils/types/DTO/users/users.dto";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

type TGetAllUsers = {
  page?: number;
  limit?: number;
  searchTerm?: string;
};

async function getAllUsers(params: TGetAllUsers) {
  const { data } = await api.get<GetAllUsersDto>(`/management/user`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllUsers(params: TGetAllUsers) {
  return useQuery({
    queryKey: ["users", params],
    queryFn: async () => await getAllUsers(params),
    placeholderData: keepPreviousData,
  });
}