import { apiSSR } from "@/services/apiSSR";
import OfficerPage from "./components/officer-page";
import { ApiQuestionsDTOProgress } from "@/utils/types/DTO/questions.dto";

export default async function Page() {
  const api = await apiSSR();
  const { data } = await api.get<ApiQuestionsDTOProgress>(
    "/officer/answer/progress",
    {
      params: {
        type: "CHECKIN",
      },
    }
  );
  return <OfficerPage progressData={data} />;
}
