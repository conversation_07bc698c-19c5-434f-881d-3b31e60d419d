import { api } from "@/services/api";
import { GetAllProficiencyDto } from "@/utils/types/DTO/profeciency/profeciency.dto";
import { useQuery } from "@tanstack/react-query";

type TGetAllProficiency = {
  searchTerm?: string;
  page?: number;
  limit?: number;
};

async function getAllProficiency(params: TGetAllProficiency) {
  const { data } = await api.get<GetAllProficiencyDto>(`/management/proficiency`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllProficiency(params: TGetAllProficiency) {
  return useQuery({
    queryKey: ["proficiency", params],
    queryFn: async () => await getAllProficiency(params),
  });
}
