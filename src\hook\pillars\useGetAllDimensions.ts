import { api } from "@/services/api";
import { useQuery } from "@tanstack/react-query";
import { GetAllDimensionsDto } from "@/utils/types/DTO/pillars/dimensions.dto";

async function getAllDimensions(searchTerm?: string) {
  const { data } = await api.get<GetAllDimensionsDto>(`/management/dimensions`, {
    params: {
      search: searchTerm,
    },
  });
  return data;
}

export function useGetAllDimensions(searchTerm?: string) {
  return useQuery({
    queryKey: ["dimensions", searchTerm],
    queryFn: async () => await getAllDimensions(searchTerm),
  });
}
