import { UserRole } from "../../enums/user-role";
import { MetaDTO } from "../meta.dto";

export type GetAllUsersDto = {
  meta: MetaDTO;
  data: GetUserDto[];
};

export type GetUserDto = {
  secureId: string;
  name: string;
  email: string;
  role: UserRole;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  adminProfile: {
    secureId: string;
    permissions: string[];
  } | null;
  respondentProfile: {
    secureId: string;
    management: string;
    managementSecureId: string;
    position: string;
    positionSecureId: string;
    joinDateBankAbc: Date | null;
    indicator1: string | null;
    indicator2: string | null;
    indicator3: string | null;
    indicator4: string | null;
    indicator5: string | null;
    indicator6: string | null;
    indicator7: string | null;
    indicator8: string | null;
    indicator9: string | null;
    indicator10: string | null;
  } | null;
};
