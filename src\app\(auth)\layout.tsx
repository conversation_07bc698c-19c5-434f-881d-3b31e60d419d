import { Flex } from "@chakra-ui/react";
import { Metadata } from "next";

type AuthLayoutContainerProps = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: "Banco ABC - Autenticação",
  description: "",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function AuthLayoutContainer({
  children,
}: AuthLayoutContainerProps) {
  return (
    <Flex
      w={"100vw"}
      minH={"100vh"}
      style={{
        background: "rgb(35,34,34)",
      }}
    >
      {children}
    </Flex>
  );
}
