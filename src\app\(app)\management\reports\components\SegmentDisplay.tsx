import { Flex, HStack, Text, VStack } from "@chakra-ui/react";
import { Segment } from "../data/types";
import { HierarchicalCheckboxCard } from "./HierarchicalCheckboxCard";

interface SegmentDisplayProps {
  segments: Segment[];
  onItemChange: (id: string, checked: boolean) => void;
  isItemSelected: (id: string) => boolean;
}

export function SegmentDisplay({
  segments,
  onItemChange,
  isItemSelected,
}: SegmentDisplayProps) {
  return (
    <Flex
      flex={1}
      gap={16}
      alignItems={"center"}
      flexDirection={"column"}
      overflowX="auto"
    >
      <Text fontSize="30px" color="white" alignSelf={"center"}>
        Escolha o Segmento e Cargo
      </Text>
      <HStack align="flex-start" gap={8} minW="fit-content">
        {segments?.map((segment) => (
          <VStack key={segment.secureId} align="stretch" minW="400px">
            <HierarchicalCheckboxCard
              id={segment.secureId}
              label={segment.segmento_nome}
              isChecked={isItemSelected(segment.secureId)}
              onChange={onItemChange}
              level="segment"
            >
              {segment.superintendencias.map((superintendencia) => (
                <HierarchicalCheckboxCard
                  key={superintendencia.secureId}
                  id={superintendencia.secureId}
                  label={superintendencia.superintendencia_nome}
                  isChecked={isItemSelected(superintendencia.secureId)}
                  onChange={onItemChange}
                  level="superintendencia"
                >
                  {superintendencia.gerencias.map((gerencia) => (
                    <HierarchicalCheckboxCard
                      key={gerencia.secureId}
                      id={gerencia.secureId}
                      label={gerencia.gerencia_nome}
                      isChecked={isItemSelected(gerencia.secureId)}
                      onChange={onItemChange}
                      level="gerencia"
                    >
                      {gerencia.cargos.map((cargo) => (
                        <HierarchicalCheckboxCard
                          key={cargo.secureId}
                          id={cargo.secureId}
                          label={cargo.cargo_nome}
                          isChecked={isItemSelected(cargo.secureId)}
                          onChange={onItemChange}
                          level="cargo"
                        />
                      ))}
                    </HierarchicalCheckboxCard>
                  ))}
                </HierarchicalCheckboxCard>
              ))}
            </HierarchicalCheckboxCard>
          </VStack>
        ))}
      </HStack>
    </Flex>
  );
}
