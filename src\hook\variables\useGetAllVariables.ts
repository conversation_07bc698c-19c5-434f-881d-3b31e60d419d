import { api } from "@/services/api";
import { GetAllVariablesDto } from "@/utils/types/DTO/hierarchy/variables.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllVariables(searchTerm?: string) {
  const { data } = await api.get<GetAllVariablesDto>(`/management/variable`, {
    params: {
      search: searchTerm,
    },
  });
  return data;
}

export function useGetAllVariables(searchTerm?: string) {
  return useQuery({
    queryKey: ["variables", searchTerm],
    queryFn: async () => await getAllVariables(searchTerm),
  });
}
