"use client";
import DefaultButton from "@/components/global/buttons/button";
import { Box, Flex, Heading, Text, VStack } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import { LuArrowLeft } from "react-icons/lu";

export default function TermsAndConditions() {
  const router = useRouter();
  return (
    <Flex
      flex={1}
      direction="column"
      align="center"
      justify="flex-start"
      p={{ base: 4, md: 8 }}
      color="white"
      overflowY="auto"
    >
      <Box maxWidth="900px" w="100%">
        <VStack align="stretch" gap={6}>
          <Heading
            as="h1"
            size="2xl"
            textAlign="center"
            color={"#a6864a"}
            mb={4}
          >
            Termos e Condições do Assessment
          </Heading>
          <Text
            fontSize="lg"
            fontStyle="italic"
            textAlign="center"
            color="gray.300"
            mb={6}
          >
            TERMO DE ADESÃO, CONFIDENCIALIDADE E CONDIÇÕES DE PARTICIPAÇÃO
            Programa de Assessment – Escola de Excelência Comercial Banco ABC
            Brasil
          </Text>

          {/* Seção 1 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              1. Finalidade
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              1.1. O presente Assessment tem como objetivo único mapear
              competências técnicas e comportamentais dos participantes,
              identificar oportunidades de desenvolvimento e apoiar decisões de
              carreira, sem caracterizar critério único de promoção, remuneração
              ou desligamento.
            </Text>
          </VStack>

          {/* Seção 2 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              2. Participantes Elegíveis
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              2.1. Podem participar colaboradores alocados nas áreas comerciais
              dos segmentos Agro, Middle e Corporate, bem como outros
              profissionais indicados.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              2.2. A participação é voluntária; ao prosseguir, o colaborador
              aceita integralmente estes Termos.
            </Text>
          </VStack>

          {/* Seção 3 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              3. Metodologia
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              3.1. O Assessment é composto por:
            </Text>
            <VStack align="stretch" pl={{ base: 6, md: 10 }}>
              <Text>a) 30 (trinta) questões situacionais on-line; </Text>
              <Text>b) 6 (seis) Vídeo-cases gravados. </Text>
            </VStack>
          </VStack>

          {/* Seção 4 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              4. Uso e Compartilhamento dos Resultados
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              4.1. Os resultados gerarão relatórios individuais e consolidados,
              acessíveis apenas ao participante, ao RH, aos gestores imediatos e
              à Diretoria Comercial.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              4.2. Os dados poderão subsidiar apenas e exclusivamente planos de
              desenvolvimento e recomendação de trilhas de capacitação,
              mentorias e on the job.
            </Text>
          </VStack>

          {/* Seção 5 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              5. Confidencialidade
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              5.1. O participante compromete-se a manter sigilo sobre questões,
              gabaritos, feedbacks, métricas e quaisquer informações obtidas
              durante ou após o Assessment.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              5.2. É vedada a gravação, reprodução ou distribuição parcial ou
              total dos materiais, salvo autorização expressa do Banco.
            </Text>
          </VStack>

          {/* Seção 6 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              6. Proteção de Dados Pessoais
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              6.1. Os dados coletados serão tratados em conformidade com a Lei
              13.709/2018 (LGPD), para as finalidades descritas na Cláusula 1.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              6.2. O titular poderá requisitar correção, eliminação ou
              portabilidade pelo e-mail <EMAIL>.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              6.3. Os dados serão mantidos pelo prazo máximo de 5 (cinco) anos
              ou outro exigido por lei.
            </Text>
          </VStack>

          {/* Seção 7 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              7. Propriedade Intelectual
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              7.1. Todos os conteúdos, marcas, metodologias e materiais
              didáticos pertencem ao Banco ABC Brasil ou a terceiros
              licenciados. O uso limita-se à participação neste Assessment.
            </Text>
          </VStack>

          {/* Seção 8 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              8. Conduta do Participante
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>8.1. É proibido:</Text>
            <VStack align="stretch" pl={{ base: 6, md: 10 }}>
              <Text>i. Solicitar ou fornecer ajuda externa;</Text>
              <Text>ii. Compartilhar usuário e senha; </Text>
              <Text>iii. Copiar, fotografar ou gravar telas;</Text>
              <Text>iv. Qualquer tentativa de fraudar resultados.</Text>
            </VStack>
            <Text pl={{ base: 2, md: 4 }}>
              8.2. Infrações poderão resultar em anulação do Assessment e
              medidas disciplinares.
            </Text>
          </VStack>

          {/* Seção 9 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              9. Suporte Técnico
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              9.1. Dúvidas ou problemas devem ser encaminhados para a área de
              Recursos Humanos.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              9.2. O Banco não se responsabiliza por falhas de internet,
              equipamento ou energia do participante.
            </Text>
          </VStack>

          {/* Seção 10 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              10. Alterações do Programa
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              10.1. O Banco reserva-se o direito de alterar, suspender ou
              cancelar o Assessment, comunicando os participantes com
              antecedência mínima de 48 horas, salvo casos de força maior.
            </Text>
          </VStack>

          {/* Seção 11 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              11. Aceite
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              11.1. Ao clicar em “Li e Aceito” ou assinar eletronicamente, o
              participante declara ter lido, compreendido e concordado com todas
              as cláusulas.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              11.2. Estes Termos entram em vigor na data do aceite e permanecem
              válidos enquanto perdurar o tratamento dos dados pessoais
              coletados.
            </Text>
          </VStack>

          {/* Seção 12 */}
          <VStack align="stretch" gap={2}>
            <Heading
              as="h2"
              size="lg"
              borderBottom="1px"
              borderColor="gray.600"
              pb={2}
            >
              12. Legislação Aplicável e Foro
            </Heading>
            <Text pl={{ base: 2, md: 4 }}>
              12.1. Aplica-se a legislação da República Federativa do Brasil.
            </Text>
            <Text pl={{ base: 2, md: 4 }}>
              12.2. Fica eleito o Foro Central da Comarca de São Paulo/SP para
              dirimir eventuais controvérsias, com renúncia a qualquer outro,
              por mais privilegiado que seja.
            </Text>
          </VStack>
          <DefaultButton
            onClick={() => router.push("/")}
            alignSelf="flex-end"
            _hover={{ bg: "gray.700" }}
          >
            <LuArrowLeft />
            Voltar para o Login
          </DefaultButton>
        </VStack>
      </Box>
    </Flex>
  );
}
