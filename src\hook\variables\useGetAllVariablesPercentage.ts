import { api } from "@/services/api";
import { GetAllVariablesPercentageDto } from "@/utils/types/DTO/hierarchy/variables.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllVariablesPercentage() {
  const { data } = await api.get<GetAllVariablesPercentageDto>(`/management/variable/total-percentage`);
  return data;
}

export function useGetAllVariablesPercentage() {
  return useQuery({
    queryKey: ["variables"],
    queryFn: async () => await getAllVariablesPercentage(),
  });
}
