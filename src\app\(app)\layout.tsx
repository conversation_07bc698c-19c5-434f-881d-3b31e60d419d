import DrawerMenu from "@/components/global/drawer/drawer-menu";
import NavLinks from "@/components/global/navlinks/navlinks";
import { Box, Flex } from "@chakra-ui/react";
import { Metadata } from "next";

type ManagementLayoutContainerProps = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: "Banco ABC",
  description: "",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function ManagementLayoutContainer({
  children,
}: ManagementLayoutContainerProps) {
  return (
    <Flex 
      w={"100vw"} 
      h={"100vh"} 
      bgColor="rgb(35,34,34)" 
      overflow="hidden"
      direction={{ base: "column", lg: "row" }}
    >
      <Box
        w={{base: "100%", lg: "auto" }}
        h={{base: "auto", lg: "100vh" }}
        position={{ base: "sticky", lg: "static" }}
        top={{ base: 0, lg: "auto" }}
        zIndex={{ base: 1000, lg: "auto" }}
      >
        <DrawerMenu children={<NavLinks />} />
      </Box>

      <Flex
        flex={1}
        marginLeft={{ base: 0, lg: 12}}
        h={{ base: "calc(100vh - 60px)", md: "100vh" }}
        overflow="auto"
        
        css={{
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#555",
            borderRadius: "4px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#666",
          },
        }}
      >
        {children}
      </Flex>
    </Flex>
  );
}
