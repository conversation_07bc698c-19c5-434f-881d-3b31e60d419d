"use client";
import {
  Box,
  Flex,
  Button,
  Grid,
  GridItem,
  HStack,
  Image,
  Input,
  Text,
  VStack,
  Checkbox,
  Link,
  Field,
  useBreakpointValue,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { useAuthContext } from "@/providers/auth-provider";
import { toaster } from "@/components/ui/toaster";
import { useRouter } from "next/navigation";

const SignInSchema = yup.object().shape({
  email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
  password: yup.string().required("A senha é obrigatória"),
});

type SignInFormData = yup.InferType<typeof SignInSchema>;

export default function Home() {
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const [agreed, setAgreed] = useState(false);
  const { signIn, isAuthenticated, user } = useAuthContext();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated) {
      if (user?.role === "ADMIN") {
        router.push("/management");
      } else if (user?.role === "USER") {
        router.push("/officer");
      }
    }
  }, [isAuthenticated, user, router]);

  const {
    register,
    handleSubmit,
    formState,
    formState: { errors },
  } = useForm<SignInFormData>({
    resolver: yupResolver(SignInSchema),
  });

  const handleSignIn = async (data: SignInFormData) => {
    try {
      if (!agreed) {
        toaster.warning({
          title:
            "Você precisa concordar com os termos e condições para continuar.",
          duration: 3000,
        });
        return;
      }
      await signIn.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <>
      {isMobile ? (
        <>
          <VStack flex={1} position={"relative"}>
            <Box
              bgImage="url(/images/login/bg-02.png)"
              w="100%"
              h="20%"
              bgRepeat="no-repeat"
              bgSize="cover"
              position="relative"
              bgPos="center"
            />
            <VStack mt={8} px={6}>
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w="80px"
                h="auto"
              />
              <Text fontSize={"lg"}>Plataforma de Assessment ABC BRASIL</Text>
              <Text
                fontSize={{ base: "md", lg: "lg" }}
                textAlign="center"
                mb={4}
                fontWeight={"bold"}
              >
                Quer evoluir na sua jornada comercial? Preencha os campos abaixo
                e descubra quais caminhos podem acelerar seu desenvolvimento.
              </Text>
            </VStack>
            <VStack w={"80%"} as="form" onSubmit={handleSubmit(handleSignIn)}>
              <Field.Root invalid={!!errors.email} width={"100%"} mb={6}>
                <Input
                  size={"sm"}
                  placeholder="email"
                  bgColor={"white"}
                  color={"gray.600"}
                  _placeholder={{
                    textAlign: "center",
                  }}
                  {...register("email")}
                />
                <Field.ErrorText>{errors.email?.message}</Field.ErrorText>
              </Field.Root>
              <Field.Root invalid={!!errors.password} width={"100%"} mb={4}>
                <Input
                  size={"sm"}
                  placeholder="senha"
                  bgColor={"white"}
                  color={"gray.600"}
                  _placeholder={{
                    textAlign: "center",
                  }}
                  type="password"
                  {...register("password")}
                />
                <Field.ErrorText>{errors.password?.message}</Field.ErrorText>
              </Field.Root>

              <HStack gap={2} mb={6} alignItems="flex-start">
                <Checkbox.Root
                  size="sm"
                  onCheckedChange={(e) => setAgreed(!!e.checked)}
                  checked={agreed}
                >
                  <Checkbox.HiddenInput />
                  <Checkbox.Control bgColor={"white"} />
                  <Checkbox.Label>
                    <Text fontSize="sm" color="white" lineHeight="1.2">
                      Eu aceito os{" "}
                      <Link
                        color="yellow.600"
                        textDecoration="underline"
                        _hover={{ color: "yellow.500" }}
                        href="terms-and-conditions"
                      >
                        Termos e Condições
                      </Link>
                    </Text>
                  </Checkbox.Label>
                </Checkbox.Root>
              </HStack>

              <Button
                type="submit"
                bg="#B8860B"
                color="white"
                borderRadius="25px"
                px={8}
                py={2}
                fontSize="sm"
                fontWeight="normal"
                _hover={{
                  bg: "#A0750A",
                }}
                _disabled={{
                  bg: "gray.400",
                  cursor: "not-allowed",
                }}
                // isDisabled={!agreed}
                loading={formState.isSubmitting}
              >
                Entrar
              </Button>
            </VStack>
          </VStack>
        </>
      ) : (
        <>
          <Flex flex={1} position={"relative"} overflow={"hidden"}>
            <Box
              position="absolute"
              top={0}
              left={0}
              transform="translateY(50%)"
              w={"100%"}
              h={"100%"}
              bgImg="url(/images/login/padraoBG.svg)"
              bgRepeat="no-repeat"
              bgPos="center bottom"
              bgSize="contain"
              clipPath="inset(0 0 50% 0)"
              display={{ base: "none", lg: "block" }}
            />
            <Grid
              templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }}
              gap={1}
              w={"100vw"}
              h="100vh"
              position={"relative"}
            >
              <GridItem>
                <VStack
                  justifyContent="center"
                  alignItems="center"
                  height="100vh"
                  w={"100%"}
                  p={{ base: 4, md: 8 }}
                >
                  <HStack
                    position={{ base: "relative", lg: "absolute" }}
                    top={0}
                    left={0}
                    w={"100%"}
                    m={{ lg: 8 }}
                    justifyContent={{ base: "center", lg: "flex-start" }}
                    alignItems="center"
                    gap={8}
                    mb={{ base: 8, lg: 0 }}
                  >
                    <Image
                      src="/images/logoBancoABC.svg"
                      alt="Banco ABC"
                      w="100px"
                      h="auto"
                    />
                    <Text fontSize={{ base: "xl", md: "2xl" }}>
                      Plataforma de Assessment ABC BRASIL
                    </Text>
                  </HStack>

                  <VStack
                    width={{ base: "100%", md: "80%", lg: "50%" }}
                    as="form"
                    onSubmit={handleSubmit(handleSignIn)}
                  >
                    <Text
                      fontSize={{ base: "md", lg: "lg" }}
                      textAlign="center"
                      mb={4}
                      fontWeight={"bold"}
                    >
                      Quer evoluir na sua jornada comercial? Preencha os campos
                      abaixo e descubra quais caminhos podem acelerar seu
                      desenvolvimento.
                    </Text>
                    <Field.Root invalid={!!errors.email} width={"100%"} mb={6}>
                      <Input
                        size={"sm"}
                        placeholder="email"
                        bgColor={"white"}
                        color={"gray.600"}
                        _placeholder={{
                          textAlign: "center",
                        }}
                        {...register("email")}
                      />
                      <Field.ErrorText>{errors.email?.message}</Field.ErrorText>
                    </Field.Root>
                    <Field.Root
                      invalid={!!errors.password}
                      width={"100%"}
                      mb={4}
                    >
                      <Input
                        size={"sm"}
                        placeholder="senha"
                        bgColor={"white"}
                        color={"gray.600"}
                        _placeholder={{
                          textAlign: "center",
                        }}
                        type="password"
                        {...register("password")}
                      />
                      <Field.ErrorText>
                        {errors.password?.message}
                      </Field.ErrorText>
                    </Field.Root>

                    <HStack gap={2} mb={6} alignItems="flex-start">
                      <Checkbox.Root
                        size="sm"
                        onCheckedChange={(e) => setAgreed(!!e.checked)}
                        checked={agreed}
                      >
                        <Checkbox.HiddenInput />
                        <Checkbox.Control bgColor={"white"} />
                        <Checkbox.Label>
                          <Text fontSize="sm" color="white" lineHeight="1.2">
                            Eu aceito os{" "}
                            <Link
                              color="yellow.600"
                              textDecoration="underline"
                              _hover={{ color: "yellow.500" }}
                              href="terms-and-conditions"
                            >
                              Termos e Condições
                            </Link>
                          </Text>
                        </Checkbox.Label>
                      </Checkbox.Root>
                    </HStack>

                    <Button
                      type="submit"
                      bg="#B8860B"
                      color="white"
                      borderRadius="25px"
                      px={8}
                      py={2}
                      fontSize="sm"
                      fontWeight="normal"
                      _hover={{
                        bg: "#A0750A",
                      }}
                      _disabled={{
                        bg: "gray.400",
                        cursor: "not-allowed",
                      }}
                      // isDisabled={!agreed}
                      loading={formState.isSubmitting}
                    >
                      Entrar
                    </Button>
                  </VStack>
                </VStack>
              </GridItem>
              <GridItem display={{ base: "none", lg: "grid" }}>
                <Box
                  bgImage="url(/images/login/bg-02.png)"
                  w="100%"
                  h="100vh"
                  bgRepeat="no-repeat"
                  bgSize="cover"
                  position="relative"
                  _after={{
                    content: '""',
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background:
                      "linear-gradient(to right, rgba(35,34,34,1) 0%, transparent 3%)",
                  }}
                />
              </GridItem>
            </Grid>
          </Flex>
        </>
      )}
    </>
  );
}
