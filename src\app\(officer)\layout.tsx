import { Flex } from "@chakra-ui/react";
import { Metadata } from "next";

type OfficerLayoutContainerProps = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: "Banco ABC - Officer",
  description: "",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function OfficerLayoutContainer({
  children,
}: OfficerLayoutContainerProps) {
  return (
    <Flex w={"100vw"} h={"100vh"} bgColor="rgb(255, 255, 255)" overflow="auto">
      {children}
    </Flex>
  );
}
