import Button from "@/components/global/buttons/button";
import { Flex, HStack, Textarea, VStack, Text, Box } from "@chakra-ui/react";
import { useEffect, useState } from "react";
type EssayQuestionProps = {
  handleChangeForObjective: (
    value: string,
    type: "essay" | "objective"
  ) => void;
  questionStatement: string;
};
export default function EssayQuestion({
  handleChangeForObjective,
  questionStatement,
}: EssayQuestionProps) {
  const [answer, setAnswers] = useState<string>("");

  useEffect(() => {
    const storedAnswer = localStorage.getItem(questionStatement);
    if (storedAnswer) {
      setAnswers(storedAnswer);
    }
  }, [answer]);

  const handleConfirm = () => {
    handleChangeForObjective(answer, "essay");
    localStorage.removeItem(questionStatement);
  };

  return (
    <>
      <VStack
        w={"100%"}
        h={"100%"}
        justifyContent="center"
        alignItems="start"
        gap={8}
      >
        <Text
          fontSize={{ base: 16, "2xl": 24 }}
          fontWeight="bold"
          color="black"
          lineHeight={1.3}
        >
          {questionStatement}
        </Text>
        <Box w={"100%"} h={"60%"} display="flex" justifyContent="center">
          <Textarea
            w={"100%"}
            h={"100%"}
            borderTop={"20px solid rgb(32,32,31)"}
            borderBottom={"20px solid rgb(32,32,31)"}
            borderTopRadius={20}
            borderBottomRadius={20}
            bgColor={"rgb(241,241,241)"}
            resize="none"
            color="black"
            p={4}
            _scrollbar={{
              backgroundColor: "rgb(225,223,224)",
            }}
            _scrollbarThumb={{
              backgroundColor: "rgb(198,198,198)",
            }}
            placeholder="Digite sua resposta..."
            value={answer}
            onChange={(e) => {
              setAnswers(e.target.value);
              localStorage.setItem(questionStatement, e.target.value);
            }}
          />
        </Box>
        <Flex width={"100%"} justifyContent={{ base: "center", lg: "flex-end"}}>
          <Button
            bgColor={"rgb(103, 42, 32)"}
            borderRadius={20}
            mb={4}
            px={8}
            py={4}
            onClick={handleConfirm}
          >
            <Text fontSize={{ base: 18, "2xl": 22 }} fontWeight={"bold"}>
              Confirmar resposta
            </Text>
          </Button>
        </Flex>
      </VStack>
    </>
  );
}
