"use client";

import { fonts } from "@/styles/fonts";
import { Provider } from "@/providers";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning={true} lang="pt-br">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={fonts.aBeeZee.className}
        style={{
          overflow: "auto",
        }}
      >
        <Provider>{children}</Provider>
      </body>
    </html>
  );
}
