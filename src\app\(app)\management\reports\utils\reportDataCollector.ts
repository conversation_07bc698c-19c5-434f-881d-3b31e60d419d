import {
  Segment,
  ReportFilter,
  ReportGenerationRequest
} from "../data/types";

/**
 * Converts selected rounds to the API format
 */
export function convertRoundsToApiFormat(selectedRounds: Set<string>): "checkIn" | "checkOut" | "both" {
  const hasCheckIn = selectedRounds.has("rodada1");
  const hasCheckOut = selectedRounds.has("rodada2");
  const hasBoth = selectedRounds.has("rodada");

  if (hasBoth || (hasCheckIn && hasCheckOut)) {
    return "both";
  } else if (hasCheckIn) {
    return "checkIn";
  } else if (hasCheckOut) {
    return "checkOut";
  } else {
    // Default to checkIn if no selection
    return "checkIn";
  }
}

/**
 * Parses a hierarchical key to extract individual secureIds
 */
function parseHierarchicalKey(key: string): {
  segment: string;
  superintendency?: string;
  management?: string;
  position?: string;
} {
  const parts = key.split('|');
  return {
    segment: parts[0],
    superintendency: parts[1],
    management: parts[2],
    position: parts[3],
  };
}

/**
 * Collects position filters from selected hierarchical keys and hierarchy data
 */
export function collectPositionFilters(
  selectedItems: Set<string>,
  hierarchyData: Segment[]
): ReportFilter[] {
  const filters: ReportFilter[] = [];

  // Process each selected hierarchical key
  selectedItems.forEach((hierarchicalKey) => {
    const parsed = parseHierarchicalKey(hierarchicalKey);

    // Only include position-level selections (keys with all 4 parts)
    if (parsed.segment && parsed.superintendency && parsed.management && parsed.position) {
      filters.push({
        segment: parsed.segment,
        superintendency: parsed.superintendency,
        management: parsed.management,
        position: parsed.position,
      });
    }
  });

  return filters;
}

/**
 * Validates if the required selections are made for report generation
 */
export function validateReportSelections(
  selectedRounds: Set<string>,
  selectedItems: Set<string>,
  hierarchyData: Segment[]
): { isValid: boolean; message?: string } {
  // Check if at least one round is selected
  const hasRoundSelection = selectedRounds.size > 0;
  if (!hasRoundSelection) {
    return {
      isValid: false,
      message: "Selecione pelo menos uma rodada (Check-in ou Check-out)",
    };
  }

  // Check if at least one position is selected
  const positionFilters = collectPositionFilters(selectedItems, hierarchyData);
  if (positionFilters.length === 0) {
    return {
      isValid: false,
      message: "Selecione pelo menos um cargo para gerar o relatório",
    };
  }

  return { isValid: true };
}

/**
 * Creates the complete report generation request payload
 */
export function createReportRequest(
  selectedRounds: Set<string>,
  selectedItems: Set<string>,
  hierarchyData: Segment[]
): ReportGenerationRequest {
  const rounds = convertRoundsToApiFormat(selectedRounds);
  const filters = collectPositionFilters(selectedItems, hierarchyData);

  return {
    rounds,
    filters,
  };
}
