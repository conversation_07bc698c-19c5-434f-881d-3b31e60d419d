import { Tooltip } from "@/components/ui/tooltip";
import { Button, ButtonProps } from "@chakra-ui/react";

interface DefaultButtonProps extends ButtonProps {
  children: React.ReactNode;
  buttonColor?: string;
  color?: string;
  tooltipContent?: string;
}

export default function DefaultButton({
  children,
  buttonColor = "#a5854a",
  color = "white",
  tooltipContent,
  ...props
}: DefaultButtonProps) {
  const buttonElement = (
    <Button
      bgColor={buttonColor}
      color={color}
      transition="all 0.2s"
      _hover={{
        filter: "brightness(85%)",
      }}
      {...props}
    >
      {children}
    </Button>
  );

  return (
    <>
      {tooltipContent ? (
        <Tooltip content={tooltipContent}>{buttonElement}</Tooltip>
      ) : (
        buttonElement
      )}
    </>
  );
}
