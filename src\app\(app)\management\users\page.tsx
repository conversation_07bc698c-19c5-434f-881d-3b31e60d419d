"use client";
import Defa<PERSON><PERSON><PERSON>on from "@/components/global/buttons/button";
import {
  Box,
  Field,
  Flex,
  HStack,
  Input,
  Stack,
  Table,
  Text,
  VStack,
  Pagination,
  ButtonGroup,
  IconButton,
} from "@chakra-ui/react";
import { useState } from "react";
import {
  LuSearch,
  LuPlus,
  LuTrash2,
  LuPencil,
  LuUserX,
  LuUserCheck,
  LuChevronLeft,
  LuChevronRight,
} from "react-icons/lu";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useGetAllUsers } from "@/hook/users/useGetAllUsers";
import { formatInTimeZone } from "date-fns-tz";
import { translateUsersRole } from "@/utils/formatters/formatUsersRole";
import BasicModal from "@/components/global/modal/basic-modal";
import { useMutation } from "@tanstack/react-query";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { GetUserDto } from "@/utils/types/DTO/users/users.dto";
import FormCombobox, {
  ComboboxOption,
} from "@/components/global/combobox/form-combobox";
import { useGetAllCategories } from "@/hook/hierarchy/useGetCategories";
import { useGetAllPermissions } from "@/hook/permissions/useGetAllPermissions";
import { AxiosError } from "axios";
import { ApiErrorInputDTO } from "@/utils/types/DTO/api-error.dto";

type NewUserFormData = {
  secureId?: string;
  name: string;
  email: string;
  password?: string;
  confirmPassword?: string;
  role: "ADMIN" | "USER";
  managementId?: string;
  positionId?: string;
  permissions?: string[];
  entryDate?: string; // data de entrada no banco
  indicator1?: number;
  indicator2?: number;
  indicator3?: number;
  indicator4?: number;
  indicator5?: number;
  indicator6?: number;
  indicator7?: number;
  indicator8?: number;
  indicator9?: number;
  indicator10?: number;
};

const NewUserSchema: yup.ObjectSchema<NewUserFormData> = yup.object().shape({
  secureId: yup.string().optional(),
  name: yup
    .string()
    .max(50, "O nome deve ter no máximo 50 caracteres")
    .required("O nome do usuário é obrigatório"),
  email: yup
    .string()
    .email("E-mail inválido")
    .required("O e-mail é obrigatório"),
  password: yup.string().when("secureId", {
    is: (val: string | undefined) => !val,
    then: (schema) =>
      schema
        .min(6, "A senha deve ter no mínimo 6 caracteres")
        .required("A senha é obrigatória"),
    otherwise: (schema) => schema.optional(),
  }),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem ser iguais")
    .when("password", {
      is: (val: string | undefined) => !!val && val.length > 0,
      then: (schema) => schema.required("A confirmação de senha é obrigatória"),
      otherwise: (schema) => schema.optional(),
    }),
  role: yup
    .string()
    .oneOf(["ADMIN", "USER"])
    .required("O papel do usuário é obrigatório"),

  managementId: yup.string().when("role", {
    is: "USER",
    then: (schema) => schema.required("A gerência é obrigatória"),
    otherwise: (schema) => schema.optional(),
  }),
  positionId: yup.string().when("role", {
    is: "USER",
    then: (schema) => schema.required("O cargo é obrigatório"),
    otherwise: (schema) => schema.optional(),
  }),
  permissions: yup
    .array()
    .of(yup.string().required())
    .when("role", {
      is: "ADMIN",
      then: (schema) =>
        schema
          .of(yup.string().required())
          .min(1, "Pelo menos uma permissão é obrigatória")
          .required("As permissões são obrigatórias"),
      otherwise: (schema) => schema.optional(),
    }),
  entryDate: yup.string().when("role", {
    is: "USER",
    then: (schema) => schema.required("A data de entrada é obrigatória"),
    otherwise: (schema) => schema.optional(),
  }),
  indicator1: yup
    .number()
    .typeError("Indicador 1 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 1 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator2: yup
    .number()
    .typeError("Indicador 2 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 2 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator3: yup
    .number()
    .typeError("Indicador 3 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 3 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator4: yup
    .number()
    .typeError("Indicador 4 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 4 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator5: yup
    .number()
    .typeError("Indicador 5 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 5 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator6: yup
    .number()
    .typeError("Indicador 6 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 6 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator7: yup
    .number()
    .typeError("Indicador 7 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 7 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator8: yup
    .number()
    .typeError("Indicador 8 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 8 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator9: yup
    .number()
    .typeError("Indicador 9 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 9 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
  indicator10: yup
    .number()
    .typeError("Indicador 10 deve ser um número")
    .when("role", {
      is: "USER",
      then: (s) => s.required("Indicador 10 é obrigatório"),
      otherwise: (s) => s.optional(),
    }),
});

const usersRoles = [
  { label: "Administrador", value: "ADMIN" },
  { label: "Usuário", value: "USER" },
];

export default function Users() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<GetUserDto | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);

  const { data: usersData } = useGetAllUsers({ searchTerm, page, limit: 6 });

  const { data: categoriesData } = useGetAllCategories({ limit: "all" });

  const { data: permissionsData } = useGetAllPermissions();

  const getManagementOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "MANAGEMENT")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getPositionOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "POSITION")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getPermissionsOptions = (): ComboboxOption[] => {
    return (
      permissionsData?.data?.map((perm) => ({
        label: perm.slug,
        value: perm.slug, // Usar slug como value para enviar para a API
      })) || []
    );
  };

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<NewUserFormData>({
    resolver: yupResolver(NewUserSchema),
    defaultValues: {
      name: "",
      email: "",
      role: undefined,
      entryDate: "",
      indicator1: 0,
      indicator2: 0,
      indicator3: 0,
      indicator4: 0,
      indicator5: 0,
      indicator6: 0,
      indicator7: 0,
      indicator8: 0,
      indicator9: 0,
      indicator10: 0,
    },
  });

  const watchedRole = watch("role");

  const addUser = useMutation({
    mutationFn: async (data: NewUserFormData) => {
      // Preparar dados baseado no tipo de usuário
      const payload: any = {
        name: data.name,
        email: data.email,
        password: data.password,
        role: data.role,
      };

      // Adicionar campos específicos baseado no role
      if (data.role === "USER") {
        payload.managementId = data.managementId;
        payload.positionId = data.positionId;
        payload.entryDate = data.entryDate;
        for (let i = 1; i <= 10; i++) {
          payload[`indicator${i}`] = (data as any)[`indicator${i}`];
        }
      } else if (data.role === "ADMIN") {
        payload.permissions = data.permissions;
      }
      await api.post("/management/user", {
        ...payload,
        // send date string, let server parse
        joinDateBankAbc: data.entryDate || undefined,
      });
    },
    onSuccess: () => {
      toaster.success({
        title: "Usuário adicionado com sucesso!",
      });
    },
    onError: (error: AxiosError<ApiErrorInputDTO>) => {
      toaster.error({
        title: "Erro ao adicionar usuário",
        description: error?.response?.data?.message,
      });
    },
  });

  const editUser = useMutation({
    mutationFn: async (data: NewUserFormData) => {
      const payload: any = {
        name: data.name,
        email: data.email,
        role: data.role,
      };

      if (data.password) {
        if (data.password !== data.confirmPassword) {
          toaster.error({
            title: "As senhas não coincidem",
            description: "Por favor, verifique as senhas e tente novamente.",
          });
          return;
        }
        payload.password = data.password;
      }

      // include date string in update
      if (data.role === "USER") {
        payload.managementId = data.managementId;
        payload.positionId = data.positionId;
        payload.entryDate = data.entryDate;
        payload.joinDateBankAbc = data.entryDate || undefined;
        for (let i = 1; i <= 10; i++) {
          payload[`indicator${i}`] = (data as any)[`indicator${i}`];
        }
      } else if (data.role === "ADMIN") {
        payload.permissions = data.permissions;
      }
      await api.patch(`/management/user/${selectedUser?.secureId}`, payload);
    },
    onSuccess: () => {
      toaster.success({
        title: "Usuário editado com sucesso!",
      });
    },
    onError: (error: AxiosError<ApiErrorInputDTO>) => {
      toaster.error({
        title: "Erro ao editar usuário",
        description: error?.response?.data?.message,
      });
    },
  });

  const deleteUser = useMutation({
    mutationFn: async () => {
      await api.delete(`/management/user/${selectedUser?.secureId}`);
    },
    onSuccess: () => {
      toaster.success({
        title: "Usuário excluído com sucesso!",
      });
    },
    onError: (error: AxiosError<ApiErrorInputDTO>) => {
      toaster.error({
        title: "Erro ao excluir usuário",
        description: error?.response?.data?.message,
      });
    },
  });

  const handleAddUser = async (data: NewUserFormData) => {
    try {
      await addUser.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["users"] });
      setIsAddModalOpen(false);
      reset();
    } catch (e) {}
  };

  const handleEditUser = async (data: NewUserFormData) => {
    try {
      await editUser.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["users"] });
      setIsEditModalOpen(false);
      reset();
    } catch (e) {}
  };

  const handleDeleteUser = async () => {
    try {
      await deleteUser.mutateAsync();
      queryClient.invalidateQueries({ queryKey: ["users"] });
      setIsDeleteModalOpen(false);
    } catch (e) {}
  };

  const toggleUserStatus = useMutation({
    mutationFn: async (user: GetUserDto) => {
      const newStatus = !user.active;
      await api.patch(`/management/user/${user.secureId}`, {
        active: newStatus,
      });
      return newStatus;
    },
    onSuccess: (newStatus) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toaster.success({
        title: `Usuário ${newStatus ? "ativado" : "inativado"} com sucesso!`,
      });
      setSelectedUser(null);
    },
    onError: (error: AxiosError<ApiErrorInputDTO>) => {
      toaster.error({
        title: "Erro ao alterar o status",
        description: error?.response?.data?.message,
      });
      setSelectedUser(null);
    },
  });

  const handleToggleUserStatus = (user: GetUserDto) => {
    setSelectedUser(user);
    toggleUserStatus.mutate(user);
  };

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (user: GetUserDto) => {
    setSelectedUser(user);
    setValue("secureId", user.secureId);
    setValue("name", user.name);
    setValue("email", user.email);
    setValue("role", user.role);
    if (user.role === "USER" && user.respondentProfile) {
      setValue("managementId", user.respondentProfile.managementSecureId);
      setValue("positionId", user.respondentProfile.positionSecureId);
      // Prepopulate join date and indicators
      const profile = user.respondentProfile;
      const dateStr = profile.joinDateBankAbc
        ? new Date(profile.joinDateBankAbc).toISOString().split("T")[0]
        : "";
      setValue("entryDate", dateStr);
      for (let i = 1; i <= 10; i++) {
        const key = `indicator${i}`;
        const val = (profile as any)[key];
        setValue(key as any, val ? Number(val) : 0);
      }
    } else if (user.role === "ADMIN" && user.adminProfile) {
      setValue("permissions", user.adminProfile.permissions);
    }
    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (user: GetUserDto) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };

  return (
    <Flex
      flex={1}
      position={"relative"}
      overflow={"hidden"}
      p={4}
      mt={{ base: 10, lg: 0 }}
    >
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Usuários
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Usuário
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflowX="auto"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Gerência/Cargo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {usersData?.data && usersData?.data.length > 0 ? (
                usersData?.data.map((item) => (
                  <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                    <Table.Cell>
                      <Text color="gray.300" fontWeight="medium">
                        {item.name}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300" maxW="200px">
                        {translateUsersRole(item.role)}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300">
                        {item.role === "USER" && item.respondentProfile
                          ? item.respondentProfile.management +
                            " / " +
                            item.respondentProfile.position
                          : "-"}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300" maxW="200px">
                        {formatInTimeZone(
                          item.createdAt,
                          "America/Sao_Paulo",
                          "dd/MM/yyyy HH:mm:ss"
                        )}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <HStack gap={2} justifyContent={"center"}>
                        <DefaultButton
                          tooltipContent="Editar"
                          buttonColor="#156082"
                          size="sm"
                          onClick={() => handleOpenEditModal(item)}
                        >
                          <LuPencil />
                        </DefaultButton>
                        <DefaultButton
                          tooltipContent="Excluir"
                          buttonColor="red.500"
                          size="sm"
                          onClick={() => handleOpenDeleteModal(item)}
                        >
                          <LuTrash2 />
                        </DefaultButton>
                        <DefaultButton
                          tooltipContent={item.active ? "Inativar" : "Ativar"}
                          buttonColor={item.active ? "gray.500" : "green.600"}
                          size="sm"
                          onClick={() => handleToggleUserStatus(item)}
                          loading={
                            toggleUserStatus.isPending &&
                            selectedUser?.secureId === item.secureId
                          }
                        >
                          {item.active ? <LuUserX /> : <LuUserCheck />}
                        </DefaultButton>
                      </HStack>
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={4}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhum usuário encontrado
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
        {usersData?.meta?.TotalPages && usersData?.meta?.TotalPages > 0 && (
          <Flex
            justifyContent={{ base: "center", lg: "flex-end" }}
            mr={{ base: 0, lg: 10 }}
          >
            <Pagination.Root
              count={usersData?.meta?.TotalItems || 0}
              pageSize={usersData?.meta?.ItemsPerPage || 0}
              page={usersData?.meta?.CurrentPage}
              onPageChange={(e) => setPage(e.page)}
              position={"end"}
            >
              <ButtonGroup variant="ghost" size="sm">
                <Pagination.PrevTrigger asChild>
                  <IconButton aria-label="Página Anterior">
                    <LuChevronLeft />
                  </IconButton>
                </Pagination.PrevTrigger>

                <Pagination.Items
                  render={(pageItem) => (
                    <IconButton
                      key={pageItem.value}
                      variant={pageItem.value === page ? "outline" : "ghost"}
                      arial-label={`Ir para a página ${pageItem.value}`}
                      borderColor={
                        pageItem.value === page ? "gray.400" : "transparent"
                      }
                      _hover={{
                        borderColor:
                          pageItem.value === page ? "blue.300" : "gray.600",
                      }}
                      _active={{
                        borderColor:
                          pageItem.value === page ? "blue.500" : "gray.700",
                      }}
                    >
                      {pageItem.value}
                    </IconButton>
                  )}
                />

                <Pagination.NextTrigger asChild>
                  <IconButton aria-label="Próximo página">
                    <LuChevronRight />
                  </IconButton>
                </Pagination.NextTrigger>
              </ButtonGroup>
            </Pagination.Root>
          </Flex>
        )}

        {/*} {totalItems > 0 && (
            <Text color="gray.400" fontSize="sm" textAlign="center">
              Mostrando {startPages + 1} a {Math.min(endPages, totalItems)} de {totalItems} usuários
              {searchTerm && ` (filtrado)`}
            </Text>
          )}*/}
      </Stack>

      {/* Add User Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Novo Usuário"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleAddUser)}
        isSubmitting={isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome do Usuário</Field.Label>
            <Input
              placeholder="Digite o nome do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.email}>
            <Field.Label color="white">Email do Usuário</Field.Label>
            <Input
              placeholder="Digite o email do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("email")}
            />
            <Field.ErrorText>{errors.email?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.password}>
            <Field.Label color="white">Senha do Usuário</Field.Label>
            <Input
              type="password"
              placeholder="Digite a senha do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("password")}
            />
            <Field.ErrorText>{errors.password?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.confirmPassword}>
            <Field.Label color="white">Confirme a Senha</Field.Label>
            <Input
              type="password"
              placeholder="Confirme a senha"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("confirmPassword")}
            />
            <Field.ErrorText>{errors.confirmPassword?.message}</Field.ErrorText>
          </Field.Root>

          <FormCombobox
            label="Papel do Usuário"
            placeholder="Selecione um papel"
            options={usersRoles}
            value={watchedRole}
            onValueChange={(value) => setValue("role", value as any)}
            error={errors.role}
            isInvalid={!!errors.role}
          />

          {/* Campos condicionais baseados no role */}
          {watchedRole === "USER" && (
            <>
              <FormCombobox
                label="Gerência do Usuário"
                placeholder="Selecione uma gerência"
                options={getManagementOptions()}
                value={watch("managementId")}
                onValueChange={(value) =>
                  setValue("managementId", value as string)
                }
                error={errors.managementId}
                isInvalid={!!errors.managementId}
              />

              <FormCombobox
                label="Cargo do Usuário"
                placeholder="Selecione um cargo"
                options={getPositionOptions()}
                value={watch("positionId")}
                onValueChange={(value) =>
                  setValue("positionId", value as string)
                }
                error={errors.positionId}
                isInvalid={!!errors.positionId}
              />
              <Field.Root invalid={!!errors.entryDate}>
                <Field.Label color="white">Data de Entrada</Field.Label>
                <Input type="date" bg="gray.700" {...register("entryDate")} />
                <Field.ErrorText>{errors.entryDate?.message}</Field.ErrorText>
              </Field.Root>
              {[...Array(10)].map((_, idx) => (
                <Field.Root
                  key={idx}
                  invalid={!!(errors as any)[`indicator${idx + 1}`]}
                >
                  <Field.Label color="white">Indicador {idx + 1}</Field.Label>
                  <Input
                    type="number"
                    bg="gray.700"
                    {...register(`indicator${idx + 1}` as any, {
                      valueAsNumber: true,
                    })}
                  />
                  <Field.ErrorText>
                    {(errors as any)[`indicator${idx + 1}`]?.message}
                  </Field.ErrorText>
                </Field.Root>
              ))}
            </>
          )}

          {watchedRole === "ADMIN" && (
            <FormCombobox
              label="Permissões do Usuário"
              placeholder="Selecione as permissões"
              options={getPermissionsOptions()}
              value={watch("permissions")}
              onValueChange={(value) =>
                setValue("permissions", value as string[])
              }
              error={errors.permissions as any}
              isInvalid={!!errors.permissions}
              multiple
            />
          )}
        </VStack>
      </BasicModal>

      {/* Edit User Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Usuário"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditUser)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome do Usuário</Field.Label>
            <Input
              placeholder="Digite o nome do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.email}>
            <Field.Label color="white">Email do Usuário</Field.Label>
            <Input
              placeholder="Digite o email do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("email")}
            />
            <Field.ErrorText>{errors.email?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.password}>
            <Field.Label color="white">Senha do Usuário</Field.Label>
            <Input
              type="password"
              placeholder="Digite a senha do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("password")}
            />
            <Field.ErrorText>{errors.password?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.confirmPassword}>
            <Field.Label color="white">Confirme a Senha</Field.Label>
            <Input
              type="password"
              placeholder="Confirme a senha"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("confirmPassword")}
            />
            <Field.ErrorText>{errors.confirmPassword?.message}</Field.ErrorText>
          </Field.Root>

          {selectedUser?.role === "USER" && (
            <>
              <FormCombobox
                label="Gerência do Usuário"
                placeholder="Selecione uma gerência"
                options={getManagementOptions()}
                value={watch("managementId")}
                onValueChange={(value) =>
                  setValue("managementId", value as string)
                }
                error={errors.managementId}
                isInvalid={!!errors.managementId}
              />
              <FormCombobox
                label="Cargo do Usuário"
                placeholder="Selecione um cargo"
                options={getPositionOptions()}
                value={watch("positionId")}
                onValueChange={(value) =>
                  setValue("positionId", value as string)
                }
                error={errors.positionId}
                isInvalid={!!errors.positionId}
              />
              <Field.Root invalid={!!errors.entryDate}>
                <Field.Label color="white">Data de Entrada</Field.Label>
                <Input type="date" bg="gray.700" {...register("entryDate")} />
                <Field.ErrorText>{errors.entryDate?.message}</Field.ErrorText>
              </Field.Root>
              {[...Array(10)].map((_, idx) => (
                <Field.Root
                  key={idx}
                  invalid={!!(errors as any)[`indicator${idx + 1}`]}
                >
                  <Field.Label color="white">Indicador {idx + 1}</Field.Label>
                  <Input
                    type="number"
                    bg="gray.700"
                    {...register(`indicator${idx + 1}` as any, {
                      valueAsNumber: true,
                    })}
                  />
                  <Field.ErrorText>
                    {(errors as any)[`indicator${idx + 1}`]?.message}
                  </Field.ErrorText>
                </Field.Root>
              ))}
            </>
          )}

          {selectedUser?.role === "ADMIN" && (
            <FormCombobox
              label="Permissões do Usuário"
              placeholder="Selecione as permissões"
              options={getPermissionsOptions()}
              value={watch("permissions")}
              onValueChange={(value) =>
                setValue("permissions", value as string[])
              }
              error={errors.permissions as any}
              isInvalid={!!errors.permissions}
              multiple
            />
          )}
        </VStack>
      </BasicModal>

      {/* Delete User Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Usuário"
        size="sm"
        handleConfirm={handleDeleteUser}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir o usuário{" "}
            <Text as="span" fontWeight="bold" color="red.400">
              "{selectedUser?.name}"
            </Text>
            ?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
