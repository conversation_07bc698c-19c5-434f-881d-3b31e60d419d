"use client";
import { ApiQuestionsDTO } from "@/utils/types/DTO/questions.dto";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import QuestionScreen from "./question-screen";
import VideoScreen from "./video-screen";
import { api } from "@/services/api";
import { useMutation } from "@tanstack/react-query";
import LoadingScreen from "./loading-screen";

export default function Question({
  questionData,
}: {
  questionData: ApiQuestionsDTO[];
}) {
  const router = useRouter();
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [currentQuestionGroupIndex, setCurrentQuestionGroupIndex] = useState(0);
  const [view, setView] = useState<"VIDEO" | "ESSAY" | "OBJECTIVE">("VIDEO");
  const [lastView, setLastView] = useState<"VIDEO" | "ESSAY" | "OBJECTIVE">(
    "VIDEO"
  );
  const [isSending, setIsSending] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    const hasCompletedQuestionnaire = localStorage.getItem("assmt_completed");
    if (hasCompletedQuestionnaire) {
      router.push("/officer/conclusion");
      return;
    }
    const savedProgress = localStorage.getItem("assmt_progress");
    if (savedProgress) {
      const { videoIndex, questionGroupIndex, currentView, time } =
        JSON.parse(savedProgress);
      setCurrentVideoIndex(videoIndex);
      setCurrentQuestionGroupIndex(questionGroupIndex);
      setView(currentView);
      setLastView(currentView);
      if (time) {
        setElapsedTime(time);
      }
    }
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isInitialized) {
      const progress = {
        videoIndex: currentVideoIndex,
        questionGroupIndex: currentQuestionGroupIndex,
        currentView: view,
        time: elapsedTime,
      };
      localStorage.setItem("assmt_progress", JSON.stringify(progress));
    }
  }, [
    currentVideoIndex,
    currentQuestionGroupIndex,
    view,
    isInitialized,
    elapsedTime,
  ]);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval> | undefined;

    const startTimer = () => {
      interval = setInterval(() => {
        setElapsedTime((prevTime) => prevTime + 1);
      }, 1000);
    };

    const stopTimer = () => {
      if (interval) {
        clearInterval(interval);
        interval = undefined;
      }
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopTimer();
      } else if (view === "ESSAY" || view === "OBJECTIVE") {
        startTimer();
      }
    };

    if ((view === "ESSAY" || view === "OBJECTIVE") && !document.hidden) {
      startTimer();
    }

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      stopTimer();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [view]);

  const currentVideo = questionData[currentVideoIndex];
  const currentQuestionGroup =
    currentVideo?.questionsGroups[currentQuestionGroupIndex];

  const totalQuestionGroups = questionData.reduce(
    (total, video) => total + video.questionsGroups.length,
    0
  );

  const globalQuestionGroupIndex =
    questionData
      .slice(0, currentVideoIndex)
      .reduce((total, video) => total + video.questionsGroups.length, 0) +
    currentQuestionGroupIndex;

  useEffect(() => {
    setIsSending(false);
  }, [setIsSending]);

  const handleNext = async () => {
    if (view === "VIDEO") {
      if (lastView === "VIDEO") {
        await api.post("/officer/answer/progress/video", {
          videoIndex: currentVideoIndex,
          questionGroupIndex: currentQuestionGroupIndex,
          type: "CHECKIN",
          time: elapsedTime,
        });
        setLastView("VIDEO");
        setView("ESSAY");
      } else {
        setLastView("VIDEO");
        setView(lastView);
      }
    } else {
      const isLastQuestionGroup =
        currentQuestionGroupIndex === currentVideo.questionsGroups.length - 1;
      const isLastVideo = currentVideoIndex === questionData.length - 1;

      if (isLastQuestionGroup) {
        if (isLastVideo) {
          setIsSending(true);
          sendAnswer();
        } else {
          setCurrentVideoIndex(currentVideoIndex + 1);
          setCurrentQuestionGroupIndex(0);
          setLastView("VIDEO");
          setView("VIDEO");
        }
      } else {
        setCurrentQuestionGroupIndex(currentQuestionGroupIndex + 1);
      }
    }
  };

  const sendApiMutate = useMutation({
    mutationFn: async () =>
      await api.post("/officer/answer/progress/finish", {
        time: elapsedTime,
        type: "CHECKIN",
      }),
    onSuccess: () => {
      localStorage.removeItem("assmt_progress");
      localStorage.setItem("assmt_completed", "true");
      router.push("/officer/conclusion");
    },
    onError: (error) => {
      console.error("Error sending answers:", error);
    },
  });

  const sendAnswer = () => {
    sendApiMutate.mutate();
  };

  const handleWatchVideoAgain = (currentView: "ESSAY" | "OBJECTIVE") => {
    setLastView(currentView);
    setView("VIDEO");
  };

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  if (!currentVideo) {
    router.push("/officer");
    return null;
  }

  if (view === "VIDEO") {
    return (
      <VideoScreen
        videoUrl={currentVideo.url}
        title={currentVideo.title}
        onNext={handleNext}
      />
    );
  }

  if (isSending || sendApiMutate.isPending) {
    return <LoadingScreen />;
  }

  if (
    (view === "ESSAY" || view === "OBJECTIVE") &&
    currentQuestionGroup &&
    !isSending
  ) {
    return (
      <QuestionScreen
        questionGroup={currentQuestionGroup}
        questionGroupIndex={globalQuestionGroupIndex}
        questionGroupsTotal={totalQuestionGroups}
        onConfirm={handleNext}
        elapsedTime={elapsedTime}
        onWatchVideoAgain={handleWatchVideoAgain}
        setView={setView}
        view={view}
      />
    );
  }

  return null;
}
