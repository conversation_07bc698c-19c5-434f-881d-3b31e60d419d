import { api } from "@/services/api";
import { LoginDTO, LoginResponseDTO } from "@/utils/types/DTO/login-dto";
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { usePathname, useRouter } from "next/navigation";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import Cookies from "js-cookie";
import { jwtDecode } from "jwt-decode";

export type UserAuthProps = {
  subject: string;
  name: string;
  role: string;
  adminViewPermissions: string[];
  exp: number;
  iat: number;
};

type AuthContextData = {
  isAuthenticated: boolean;
  user: UserAuthProps | undefined;
  viewPermissions: string[];
  signOut: () => void;
  signIn: UseMutationResult<void, AxiosError<any, any>, LoginDTO, unknown>;
};

type AuthProviderProps = {
  userLooged?: UserAuthProps;
  children: ReactNode;
};

const AuthContext = createContext({} as AuthContextData);

export function AuthProvider({ children }: AuthProviderProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [user, setUser] = useState<UserAuthProps | undefined>();

  const isAuthenticated = useMemo(() => {
    return !!user;
  }, [user]);

  const signIn = useMutation({
    mutationFn: async (data: LoginDTO) => {
      const response = await api.post<LoginResponseDTO>("auth/login", {
        email: data.email,
        password: data.password,
      });

      const { access_token } = response.data;

      Cookies.set("__ASSMT_TOKEN", access_token, {
        expires: 7,
        path: "/",
      });

      const decode: UserAuthProps = jwtDecode(access_token);

      setUser(decode);

      api.defaults.headers.common["Authorization"] = `Bearer ${access_token}`;

      await new Promise((resolve) => setTimeout(resolve, 2000));
    },
    onSuccess: () => {
      const role = user!.role;

      if (role === "ADMIN") {
        router.push("/management");
      }
      if (role === "USER") {
        router.push("/officer");
      }
    },
    onError: (error: AxiosError<any>) => {},
  });

  function signOut() {
    Cookies.remove("__ASSMT_TOKEN");
    setUser(undefined);
    router.push("/");
  }

  useEffect(() => {
    const token = Cookies.get("__ASSMT_TOKEN");

    const publicRoutes = ["/", "/terms-and-conditions"];

    if (token) {
      const decode: UserAuthProps = jwtDecode(token);
      setUser(decode);
      api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    } else {
      if (!publicRoutes.includes(pathname)) {
        signOut();
      }
    }
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        viewPermissions: user?.adminViewPermissions || [],
        signOut,
        signIn,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  return useContext(AuthContext);
}
