import { api } from "@/services/api";
import { GetAllPermissionsDto } from "@/utils/types/DTO/permissions/permissions.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllPermissions() {
  const { data } = await api.get<GetAllPermissionsDto>(`/management/permissions`);
  return data;
}

export function useGetAllPermissions() {
  return useQuery({
    queryKey: ["permissions"],
    queryFn: async () => await getAllPermissions(),
  });
}