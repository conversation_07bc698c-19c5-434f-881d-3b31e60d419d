import Button from "@/components/global/buttons/button";
import { toaster } from "@/components/ui/toaster";
import { VStack, Text, RadioGroup, Flex, HStack, Box } from "@chakra-ui/react";
import { useEffect, useState } from "react";

const returnLabelByOrder = (order: number) => {
  switch (order) {
    case 0:
      return "A";
    case 1:
      return "B";
    case 2:
      return "C";
    case 3:
      return "D";
    case 4:
      return "E";
    default:
      return "";
  }
};

type ObjectiveQuestionProps = {
  handleSaveAnswer?: (value: string, type: "essay" | "objective") => void;
  options: {
    secureId: string;
    description: string;
    order: number;
  }[];
  questionStatement: string;
};

export default function ObjectiveQuestion({
  handleSaveAnswer,
  options,
  questionStatement,
}: ObjectiveQuestionProps) {
  const [selectedValue, setSelectedValue] = useState<number>();

  useEffect(() => {
    const storedAnswer = localStorage.getItem(questionStatement);
    if (storedAnswer) {
      setSelectedValue(Number(storedAnswer));
    }
  }, [selectedValue]);

  const handleConfirm = () => {
    if (selectedValue === undefined) {
      toaster.error({
        title: "Alternativa não selecionada!",
        description: "Por favor, escolha a alternativa antes de continuar.",
      });
      return;
    }
    if (selectedValue !== undefined) {
      const selectedOption = options.find(
        (option) => option.order === selectedValue
      );
      if (selectedOption) {
        handleSaveAnswer?.(selectedOption.secureId, "objective");
      }
      setSelectedValue(undefined); // Reset selected value after confirmation
      localStorage.removeItem(questionStatement);
    }
  };

  return (
    <>
      <VStack
        w={{ "2xl": "100%", base: "100%" }}
        h={"100%"}
        justifyContent={{ base: "start", "2xl": "center"}}
        alignItems="start"
        gap={{
          base: 4,
          "2xl": 6,
        }}
      >
        <Text
          fontSize={{ base: 16, "2xl": 24 }}
          fontWeight="bold"
          color="black"
          lineHeight={1.3}
          textAlign={"justify"}
        >
          {questionStatement}
        </Text>
        <VStack w={"100%"} gap={4}>
          {options?.map((option) => {
            return (
              <HStack
                w={"100%"}
                gap={{
                  base: 2,
                  "2xl": 4,
                }}
                border={"1px solid black"}
                rounded={{ base: "3xl", "2xl": "full"}}
                p={2}
                display="flex"
                alignItems={{base: "start", "2xl": "center"}}
                justifyContent="flex-start"
                key={option.secureId}
                onClick={(e) => {
                  setSelectedValue(option.order);
                  localStorage.setItem(
                    questionStatement,
                    option.order.toString()
                  );
                }}
                cursor="pointer"
                _hover={{ bgColor: "gray.100" }}
              >
                <Box
                  border={{
                    base: "1px solid black",
                    "2xl": "2px solid black",
                  }}
                  bgColor={
                    selectedValue === option.order
                      ? "rgb(103, 42, 32)"
                      : "white"
                  }
                  width={{ base: "30px", "2xl": "50px" }}
                  height={{ base: "30px", "2xl": "50px" }}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  flexShrink={0}
                  rounded="full"
                >
                  <Text
                    color={selectedValue === option.order ? "white" : "black"}
                    fontSize={{ base: "sm", "2xl": "2xl" }}
                  >
                    {returnLabelByOrder(option.order)}
                  </Text>
                </Box>
                <Flex flex={1} justifyContent="center">
                  <Text
                    color={"black"}
                    fontSize={{ "2xl": "md", base: "xs" }}
                    textAlign={"justify"}
                    pr={{ base: 2, "2xl": 4 }}
                  >
                    {option.description}
                  </Text>
                </Flex>
              </HStack>
            );
          })}
        </VStack>
        <Flex width={"100%"} justifyContent={{ base: "center", lg: "flex-end"}}>
          <Button
            bgColor={"rgb(103, 42, 32)"}
            borderRadius={20}
            mb={4}
            px={8}
            py={4}
            onClick={handleConfirm}
          >
            <Text fontSize={{ base: 18, "2xl": 22 }} fontWeight={"bold"}>
              Confirmar resposta
            </Text>
          </Button>
        </Flex>
      </VStack>
    </>
  );
}
