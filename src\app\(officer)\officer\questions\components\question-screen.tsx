import Button from "@/components/global/buttons/button";
import { VStack, Image, HStack, Text, Box, Flex, useBreakpointValue } from "@chakra-ui/react";
import { FaPlay } from "react-icons/fa";
import { IoIosTimer } from "react-icons/io";
import EssayQuestion from "./essay-question";
import ObjectiveQuestion from "./objective-question";
import { ApiQuestionsDTO } from "@/utils/types/DTO/questions.dto";
import { useEffect, useState } from "react";
import { formatTime } from "@/utils/funcs/format-timer";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";

type QuestionScreenProps = {
  questionGroup: ApiQuestionsDTO["questionsGroups"][0];
  questionGroupsTotal: number;
  questionGroupIndex: number;
  onConfirm: () => void;
  onWatchVideoAgain: (currentView: "ESSAY" | "OBJECTIVE") => void;
  setView: (view: "ESSAY" | "OBJECTIVE") => void;
  view?: "ESSAY" | "OBJECTIVE";
  elapsedTime?: number;
};

export default function QuestionScreen({
  questionGroup,
  questionGroupIndex = 0,
  questionGroupsTotal = 30,
  onConfirm,
  onWatchVideoAgain,
  elapsedTime = 0,
  setView,
  view = "ESSAY",
}: QuestionScreenProps) {
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const [type, setType] = useState<"essay" | "objective">();

  useEffect(() => {
    setType(view === "ESSAY" ? "essay" : "objective");
  }, [view]);

  const handleChangeQuestion = async (
    value: string,
    type: "essay" | "objective"
  ) => {
    if (!value) {
      toaster.error({
        title: "Resposta não preenchida",
        description: "Por favor, preencha a resposta antes de continuar.",
      });
      return;
    }
    const getProgress = localStorage.getItem("assmt_progress");
    const parsedAnswers = getProgress ? JSON.parse(getProgress) : {};
    await api.post("/officer/answer", {
      questionGroupId: questionGroup.secureId,
      answer: value,
      type,
      videoIndex: parsedAnswers.videoIndex || 0,
      questionGroupIndex: parsedAnswers.questionGroupIndex || 0,
      currentView: type === "essay" ? "ESSAY" : "OBJECTIVE",
      time: elapsedTime,
    });
    if (type === "essay") {
      setView("OBJECTIVE");
      setType("objective");
    } else if (type === "objective") {
      setView("ESSAY");
      setType("essay");
      onConfirm();
    }
  };

  return (
    <>
    {!isMobile ? (
      <>
        <VStack
          flex={1}
          width={"100vw"}
          height={"100vh"}
          position="relative"
          px={20}
          py={{
            base: 5,
            "2xl": 10,
          }}
        >
          <HStack
            w={"100%"}
            justifyContent="space-between"
            mb={{
              base: 4,
              "2xl": 4,
            }}
          >
            <HStack gap={16} alignItems="center">
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w={{ base: "60px", "2xl": "100px" }}
                h="auto"
                filter={
                  "brightness(0) saturate(100%) invert(11%) sepia(4%) saturate(0%) hue-rotate(314deg) brightness(96%) contrast(88%)"
                }
              />
              <Text
                fontSize={{ base: 20, "2xl": 24 }}
                fontWeight={"bold"}
                color="black"
              >
                Questão {questionGroupIndex + 1}/{questionGroupsTotal}
              </Text>
              <Button
                pl={0}
                borderRadius={8}
                bgColor={"rgb(32,32,31)"}
                onClick={() =>
                  onWatchVideoAgain(type === "essay" ? "ESSAY" : "OBJECTIVE")
                }
              >
                <Flex alignItems="center" justifyContent={"space-between"} gap={2}>
                  <Box
                    bgColor={"rgb(212,212,212)"}
                    color={"black"}
                    p={2}
                    borderRadius={8}
                  >
                    <FaPlay />
                  </Box>
                  <Text fontSize={"md"} color={"white"} fontWeight={"bold"}>
                    Assistir video novamente
                  </Text>
                </Flex>
              </Button>
            </HStack>

            <HStack
              color={"rgb(32,32,31)"}
              fontSize={{ base: 20, "2xl": 24 }}
              gap={3}
              alignItems="center"
              justifyContent="center"
            >
              {formatTime(elapsedTime)}
              <IoIosTimer size={40} />
            </HStack>
          </HStack>
          {type === "essay" ? (
            <EssayQuestion
              handleChangeForObjective={handleChangeQuestion}
              questionStatement={questionGroup.essayQuestion}
            />
          ) : (
            <ObjectiveQuestion
              handleSaveAnswer={handleChangeQuestion}
              options={questionGroup.objectiveQuestionChoices}
              questionStatement={questionGroup.objectiveQuestion}
            />
          )}
        </VStack>
      </>
    ) : (
      <>
        <VStack
          flex={1}
          width={"100vw"}
          height={"100vh"}
          px={10}
          py={{
            base: 5,
            "2xl": 10,
          }}
        >
          <VStack
            w={"100%"}
            gap={4}
            mb={{
              base: 4,
              "2xl": 4,
            }}
          >
            <HStack gap={16} alignItems={"center"}>
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w={{ base: "60px", "2xl": "100px" }}
                h="auto"
                filter={
                  "brightness(0) saturate(100%) invert(11%) sepia(4%) saturate(0%) hue-rotate(314deg) brightness(96%) contrast(88%)"
                }
              />
              <Button
                pl={0}
                h={"36px"}
                pr={1}
                border={"none"}
                borderRadius={8}
                bgColor={"rgb(32,32,31)"}
                onClick={() =>
                  onWatchVideoAgain(type === "essay" ? "ESSAY" : "OBJECTIVE")
                }
              >
                <Flex alignItems="center" justifyContent={"space-between"} gap={1}>
                  <Box
                    bgColor={"rgb(212,212,212)"}
                    color={"black"}
                    p={2}
                    borderRadius={8}
                  >
                    <FaPlay />
                  </Box>
                  <Text fontSize={"xs"} color={"white"} fontWeight={"bold"}>
                    Assistir video novamente
                  </Text>
                </Flex>
              </Button>
            </HStack>

            <HStack gap={10}>
              <Text
                fontSize={{ base: 20, "2xl": 24 }}
                fontWeight={"bold"}
                color="black"
              >
                Questão {questionGroupIndex + 1}/{questionGroupsTotal}
              </Text>           
              <HStack
                color={"rgb(32,32,31)"}
                fontSize={{ base: 20, "2xl": 24 }}
                gap={3}
                alignItems="center"
                justifyContent="center"
              >
                {formatTime(elapsedTime)}
                <IoIosTimer size={40} />
              </HStack>
            </HStack>
             
          </VStack>
          {type === "essay" ? (
            <EssayQuestion
              handleChangeForObjective={handleChangeQuestion}
              questionStatement={questionGroup.essayQuestion}
            />
          ) : (
            <ObjectiveQuestion
              handleSaveAnswer={handleChangeQuestion}
              options={questionGroup.objectiveQuestionChoices}
              questionStatement={questionGroup.objectiveQuestion}
            />
          )}
        </VStack>
      </>
    )}
    </>
  );
}
