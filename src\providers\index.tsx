"use client";

import {
  ColorModeProvider,
  ColorModeProviderProps,
} from "@/components/ui/color-mode";
import { Toaster } from "@/components/ui/toaster";
import { ChakraProvider, defaultSystem } from "@chakra-ui/react";
import { AuthProvider } from "./auth-provider";
import ReactQueryProvider from "./react-query-provider";

export function Provider(props: ColorModeProviderProps) {
  return (
    <ReactQueryProvider>
      <AuthProvider>
        <ChakraProvider value={defaultSystem}>
          <ColorModeProvider forcedTheme="dark" {...props} />
          <Toaster />
        </ChakraProvider>
      </AuthProvider>
    </ReactQueryProvider>
  );
}
function extendTheme(arg0: {
  config: { initialColorMode: string; useSystemColorMode: boolean };
}) {
  throw new Error("Function not implemented.");
}
