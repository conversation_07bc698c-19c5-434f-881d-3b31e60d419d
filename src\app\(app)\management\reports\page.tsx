"use client";
import { <PERSON><PERSON>, <PERSON><PERSON>tack, Spinner, Text, VStack } from "@chakra-ui/react";
import { useHierarchicalSelection } from "./hooks/useHierarchicalSelection";
import { useRoundSelection } from "./hooks/useRoundSelection";
import { ReportsHeader } from "./components/ReportsHeader";
import { ReportsFilters } from "./components/ReportsFilters";
import { SegmentDisplay } from "./components/SegmentDisplay";
import { ReportResults } from "./components/ReportResults";
import { useState } from "react";
import { useGetHierarchyReports } from "@/hook/reports/useGetHierarchyReports";
import { useGenerateReport } from "@/hook/reports/useGenerateReport";
import {
  validateReportSelections,
  createReportRequest,
} from "./utils/reportDataCollector";
import { ReportGenerationResponse } from "./data/types";
import { toaster } from "@/components/ui/toaster";

export default function Reports() {
  const [searchTerm, setSearchTerm] = useState("");
  const [reportData, setReportData] = useState<ReportGenerationResponse | null>(
    null
  );
  const [showResults, setShowResults] = useState(false);

  const { data: hierarchyReportsData, isLoading } = useGetHierarchyReports();

  console.log("hierarchyReportsData: ", hierarchyReportsData);

  const {
    selectedItems,
    allSelected,
    handleItemChange,
    handleAllChange,
    isItemSelected,
  } = useHierarchicalSelection(hierarchyReportsData?.data || []);

  console.log("selectedItems: ", selectedItems);

  const { selectedRounds, handleRoundChange } = useRoundSelection();

  const generateReportMutation = useGenerateReport();

  // Validation for Generate Report button
  const isGenerateDisabled =
    !hierarchyReportsData?.data ||
    selectedRounds.size === 0 ||
    selectedItems.size === 0;

  // Handle report generation
  const handleGenerateReport = async () => {
    if (!hierarchyReportsData?.data) {
      toaster.error({
        title: "Erro",
        description: "Dados da hierarquia não carregados",
      });
      return;
    }

    // Validate selections
    const validation = validateReportSelections(
      selectedRounds,
      selectedItems,
      hierarchyReportsData.data
    );

    if (!validation.isValid) {
      toaster.error({
        title: "Seleção inválida",
        description: validation.message,
      });
      return;
    }

    try {
      // Create request payload
      const requestData = createReportRequest(
        selectedRounds,
        selectedItems,
        hierarchyReportsData.data
      );

      // Generate report
      const response = await generateReportMutation.mutateAsync(requestData);
      setReportData(response);
      setShowResults(true);
    } catch (error) {
      console.error("Error generating report:", error);
    }
  };

  if (isLoading || !hierarchyReportsData) {
    return (
      <Flex flex={1} justify="center" align="center">
        <Spinner size="xl" />
      </Flex>
    );
  }

  return (
    <Flex flex={1} position="relative" overflowX="auto">
      <VStack w="100%" gap={6} align="stretch">
        <VStack p={6} align="stretch">
          {/* Header */}
          <ReportsHeader
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onGenerateReport={handleGenerateReport}
            isGenerateDisabled={isGenerateDisabled}
            isGenerating={generateReportMutation.isPending}
          />

          {/* Main Content */}
          <HStack align="flex-start" gap={8}>
            {/* Controls */}
            <ReportsFilters
              allSelected={allSelected}
              selectedRounds={selectedRounds}
              onAllChange={handleAllChange}
              onRoundChange={handleRoundChange}
            />

            {/* Segments */}
            <SegmentDisplay
              segments={hierarchyReportsData?.data}
              onItemChange={handleItemChange}
              isItemSelected={isItemSelected}
            />
          </HStack>
        </VStack>

        {/* Report Results - Only shown after successful report generation */}
        {showResults && reportData ? (
          <ReportResults reportData={reportData} />
        ) : null}
      </VStack>
    </Flex>
  );
}
