import { apiSSR } from "@/services/apiSSR";
import { ApiQuestionsDTO } from "@/utils/types/DTO/questions.dto";
import Question from "./components/question";

export default async function Page() {
  const api = await apiSSR();
  const { data } = await api.get<ApiQuestionsDTO[]>("/officer/questions");
  if (!data || data.length === 0) {
    return <div>No questions available</div>;
  }

  return <Question questionData={data} />;
}
