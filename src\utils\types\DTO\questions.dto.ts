export type ApiQuestionsDTO = {
  secureId: string;
  title: string;
  url: string;
  questionsGroups: {
    secureId: string;
    essayQuestion: string;
    objectiveQuestion: string;
    objectiveQuestionChoices: {
      secureId: string;
      description: string;
      order: number;
    }[];
  }[];
};

export type ApiQuestionsDTOProgress = {
  questionGroupIndex: number;
  videoIndex: number;
  currentView: "ESSAY" | "OBJECTIVE";
  time: number;
  isFinished: boolean;
};
