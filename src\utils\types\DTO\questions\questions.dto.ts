import { UserRole } from "../../enums/user-role";
import { MetaDTO } from "../meta.dto";

export type GetAllQuestionsDto = {
  meta: MetaDTO;
  data: GetQuestionDto[];
};

export type GetQuestionDto = {
  secureId: string;
  order: number;
  videoId: string;
  essayQuestion: GetEssayQuestionDto;
  objectiveQuestion: GetObjectiveQuestionDto;
  updatedAt: string;
  createdAt: string;
};

type GetEssayQuestionDto = {
  secureId: string;
  statement: string;
};

type GetObjectiveQuestionDto = {
  secureId: string;
  statement: string;
  choicesCount: number;
  variables: Variable[];
};

type Variable = {
  secureId: string;
  variable: string;
  percent: string;
};

export type GetAllQuestionsChoicesDto = {
  data: GetQuestionChoiceDto[];
}

type GetQuestionChoiceDto = {
  secureId: string;
  name: string;
  order: number;
  proficiency: {
    secureId: string;
    name: string;
  }
};
