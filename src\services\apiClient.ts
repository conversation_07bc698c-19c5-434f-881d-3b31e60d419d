import axios, { AxiosError } from "axios";
import { redirect } from "next/navigation";
import { AuthTokenError } from "./errors/AuthTokenError";
import Cookies from "js-cookie";
import { toaster } from "@/components/ui/toaster";
import { ApiErrorInputDTO } from "@/utils/types/DTO/api-error.dto";

export function setupApiClient() {
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
  });

  const cookie = Cookies.get("__ASSMT_TOKEN");

  if (cookie) {
    api.defaults.headers.common["Authorization"] = `Bearer ${cookie}`;
  }

  api.interceptors.response.use(
    (response) => {
      return response;
    },
    (error: AxiosError<ApiErrorInputDTO>) => {
      // Handle 403 Forbidden - No permission
      if (error?.response?.status === 403) {
        if (!(typeof window === "undefined")) {
          console.log("Forbidden access (403), removing token");
          Cookies.remove("__ASSMT_TOKEN");
          redirect("/");
        } else {
          return Promise.reject(new AuthTokenError());
        }
      }

      if (error?.response?.data) {
        let message;

        if (Array.isArray(error.response.data.message)) {
          message = error.response.data.message.map((item) => item.message);
        } else {
          message = error.response.data.message;
        }

        if (error?.response?.status === 401) {
          toaster.error({
            title: "Não autorizado",
            description: message || "Usuário não autorizado",
          });
        } else if (error?.response?.status === 403) {
          toaster.error({
            title: "Erro",
            description: message || "Sem permissão para acessar esse recurso",
          });
        } else if (error?.response?.status === 402) {
          toaster.error({
            title: "Erro",
            description: message || "Erro inesperado",
          });
        } else {
          toaster.error({
            title: "Erro",
            description: message || "Erro inesperado",
          });
        }
      }

      return Promise.reject(error);
    }
  );

  return api;
}
