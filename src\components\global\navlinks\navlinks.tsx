"use client";
import {
  Accordion,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Text,
  VStack,
} from "@chakra-ui/react";
import { NavLink } from "./navlink";
import { useAuthContext } from "@/providers/auth-provider";

export default function NavLinks() {
  const { signOut } = useAuthContext();
  return (
    <VStack
      flex={1}
      alignContent="center"
      justifyContent={"space-between"}
      w="100%"
      h="100%"
      p={3}
      gap={0}
    >
      <Stack w="100%">
        <NavLink href="/management" shouldMatchExactHref={true}>
          Dashboard
        </NavLink>
        <NavLink href="/management/users" shouldMatchExactHref={true}>
          Usuários
        </NavLink>
        <NavLink href="/management/proficiency" shouldMatchExactHref={true}>
          Proficiência
        </NavLink>
        <Stack gap={0}>
          <Accordion.Root
            variant={"plain"}
            collapsible
            defaultValue={["hierarchy"]}
          >
            <Accordion.Item value={"hierarchy"}>
              <Accordion.ItemTrigger
                w={"100%"}
                px={4}
                py={0}
                cursor={"pointer"}
                justifyContent={"space-between"}
                _hover={{
                  textDecoration: "none",
                  background: "#000000",
                  borderRadius: "5px",
                }}
              >
                <Text
                  fontSize={"20px"}
                  fontWeight={"light"}
                  height={11}
                  color={"text"}
                  transition="200ms"
                  alignContent={"center"}
                >
                  Hierarquia
                </Text>
                <Accordion.ItemIndicator />
              </Accordion.ItemTrigger>
              <Accordion.ItemContent>
                <NavLink
                  href="/management/categories"
                  shouldMatchExactHref={true}
                  p={8}
                  py={0}
                >
                  Categorias
                </NavLink>
                <NavLink
                  href="/management/structure"
                  shouldMatchExactHref={true}
                  p={8}
                  py={0}
                >
                  Estrutura
                </NavLink>
              </Accordion.ItemContent>
            </Accordion.Item>
          </Accordion.Root>
        </Stack>
        <Stack gap={0}>
          <Accordion.Root
            variant={"plain"}
            collapsible
            defaultValue={["Pillars"]}
          >
            <Accordion.Item value={"Pillars"}>
              <Accordion.ItemTrigger
                w={"100%"}
                px={4}
                py={0}
                cursor={"pointer"}
                justifyContent={"space-between"}
                _hover={{
                  textDecoration: "none",
                  background: "#000000",
                  borderRadius: "5px",
                }}
              >
                <Text
                  fontSize={"20px"}
                  fontWeight={"light"}
                  height={11}
                  color={"text"}
                  transition="200ms"
                  alignContent={"center"}
                >
                  Pilar
                </Text>
                <Accordion.ItemIndicator />
              </Accordion.ItemTrigger>
              <Accordion.ItemContent>
                <NavLink
                  href="/management/dimensions"
                  shouldMatchExactHref={true}
                  p={8}
                  py={0}
                >
                  Dimensões
                </NavLink>
                <NavLink
                  href="/management/foundation"
                  shouldMatchExactHref={true}
                  p={8}
                  py={0}
                >
                  Fundamentos
                </NavLink>
                <NavLink
                  href="/management/variables"
                  shouldMatchExactHref={true}
                  p={8}
                  py={0}
                >
                  Variáveis
                </NavLink>
              </Accordion.ItemContent>
            </Accordion.Item>
          </Accordion.Root>
        </Stack>
        <NavLink href="/management/videos" shouldMatchExactHref={true}>
          Vídeos
        </NavLink>
        <NavLink href="/management/questions" shouldMatchExactHref={true}>
          Questões
        </NavLink>
        <NavLink href="/management/reports" shouldMatchExactHref={true}>
          Relatórios
        </NavLink>
      </Stack>

      <Button
        onClick={() => signOut()}
        justifyContent="flex-start"
        w="100%"
        variant="ghost"
        color="white"
        _hover={{
          bg: "#B8860B",
        }}
        fontSize="20px"
        fontWeight="light"
        height={11}
        px={4}
      >
        Sair
      </Button>
    </VStack>
  );
}
