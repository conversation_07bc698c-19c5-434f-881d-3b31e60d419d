import { api } from "@/services/api";
import { GetAllQuestionsChoicesDto } from "@/utils/types/DTO/questions/questions.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllQuestionsChoices(questionSecureId?: string) {
  const { data } = await api.get<GetAllQuestionsChoicesDto>(`/management/question-choice/${questionSecureId}`, {
  });
  return data;
}

export function useGetAllQuestionsChoices(questionSecureId?: string) {
  return useQuery({
    queryKey: ["questions", "choices", questionSecureId],
    queryFn: async () => await getAllQuestionsChoices(questionSecureId),
    enabled: !!questionSecureId,
  });
}