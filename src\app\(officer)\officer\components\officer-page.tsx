"use client";
import { useState, useEffect } from "react";
import { useAuthContext } from "@/providers/auth-provider";
import { Box } from "@chakra-ui/react";
import DefaultScreen from "./default-screen";
import PreStartScreen from "./pre-start-screen";
import { useRouter } from "next/navigation";
import { ApiQuestionsDTOProgress } from "@/utils/types/DTO/questions.dto";

type OfficerPageProps = {
  progressData?: ApiQuestionsDTOProgress;
};

export default function OfficerPage({ progressData }: OfficerPageProps) {
  const { user } = useAuthContext();
  const router = useRouter();
  const [currentScreen, setCurrentScreen] = useState(0);

  // Reset para a primeira tela quando o usuário logar ou entrar no site
  useEffect(() => {
    setCurrentScreen(0);
    const handleReturnToQuestionnaire = () => {
      const hasStartedQuestionnarie = localStorage.getItem("assmt_progress");
      const hasCompletedQuestionnarie = localStorage.getItem("assmt_completed");
      if (hasStartedQuestionnarie) {
        router.push("/officer/questions");
        return;
      }
      if (hasCompletedQuestionnarie) {
        router.push("/officer/conclusion");
        return;
      }
    };
    const handleSaveToLocalStorage = () => {
      if (!progressData) return;
      localStorage.removeItem("assmt_progress");
      if (progressData.isFinished) {
        localStorage.setItem("assmt_completed", "true");
        return;
      }
      const progress = {
        videoIndex: progressData.videoIndex || 0,
        questionGroupIndex: progressData.questionGroupIndex || 0,
        currentView: progressData.currentView,
        time: progressData.time || 0,
      };
      localStorage.setItem("assmt_progress", JSON.stringify(progress));
    };
    handleSaveToLocalStorage();
    handleReturnToQuestionnaire();
  }, [user, progressData]);

  const nextScreen = () => {
    if (currentScreen < 2) {
      setCurrentScreen(currentScreen + 1);
    } else if (currentScreen === 2) {
      router.push("/officer/questions");
    }
  };

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 0:
        return (
          <DefaultScreen
            topText="Este é o primeiro passo para potencializar suas competências e alinhá-las à ambição do ABC BRASIL."
            bottomText={
              <>
                A plataforma de assessment da Escola de Excelência Comercial foi
                cuidadosamente desenvolvida para refletir os desafios do nosso
                mercado e ajudá-lo a identificar oportunidades que impulsionem
                sua carreira e gerem impacto significativo nos resultados.
                <br />
                <br />
                Assista ao vídeo e depois clique em "Continuar" para iniciar sua
                jornada de autoavaliação.
              </>
            }
            buttonText="Continuar"
            videoSrc=""
            userName={user?.name}
            isInverted={false}
            onButtonClick={nextScreen}
          />
        );
      case 1:
        return (
          <DefaultScreen
            topText="No ABC BRASIL, acreditamos que a evolução começa com uma análise estratégica"
            bottomText={
              <>
                Este assessment vai além de medir competências; ele é uma
                ferramenta que conecta suas habilidades às metas corporativas,
                contribuindo diretamente para o sucesso de nossos clientes e do
                banco. Clique no vídeo e confira o que nosso VP tem a dizer.
                <br />
                <br />
                Pronto para começar?
                <br />
                Siga em frente e descubra como o processo foi desenvolvido
                especialmente para você.
              </>
            }
            buttonText="Continuar"
            videoSrc=""
            isInverted={true}
            onButtonClick={nextScreen}
          />
        );
      case 2:
        return <PreStartScreen onButtonClick={nextScreen} />;
      default:
        return null;
    }
  };

  return <Box height="100vh">{renderCurrentScreen()}</Box>;
}
