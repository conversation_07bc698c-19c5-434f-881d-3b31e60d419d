import { ReportsData } from "@/app/(app)/management/reports/data/types";
import { api } from "@/services/api";
import { useQuery } from "@tanstack/react-query";

async function getHierarchyReports() {
  const { data } = await api.get<ReportsData>(`/management/hierarchy-structure/report`);
  return data;
}

export function useGetHierarchyReports() {
  return useQuery({
    queryKey: ["hierarchy-reports"],
    queryFn: async () => await getHierarchyReports(),
  });
}