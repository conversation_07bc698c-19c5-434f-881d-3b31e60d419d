import { api } from "@/services/api";
import { useQuery } from "@tanstack/react-query";
import { useGetAllVariableDto } from "@/utils/types/DTO/pillars/variable.dto";

type TGetAllVariable = {
  searchTerm?: string;
  page?: number;
  limit?: number;
};

async function getAllVariable(params: TGetAllVariable) {
  const { data } = await api.get<useGetAllVariableDto>(`/management/variable`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllVariable(params: TGetAllVariable) {
  return useQuery({
    queryKey: ["variable", params],
    queryFn: async () => await getAllVariable(params),
  });
}
