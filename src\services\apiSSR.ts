import axios, { AxiosError } from "axios";
import { cookies } from "next/headers";
import { AuthTokenError } from "./errors/AuthTokenError";

export async function setupApiSSR() {
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
  });
  const cookiesStore = await cookies();

  const cookie = cookiesStore.get("__ASSMT_TOKEN")?.value;

  if (cookie) {
    api.defaults.headers.common["Authorization"] = `Bearer ${cookie}`;
  }

  api.interceptors.response.use(
    (response) => {
      return response;
    },
    (error: AxiosError) => {
      if (error?.response?.status === 401) {
        return Promise.reject(new AuthTokenError());
      }

      return Promise.reject(error);
    }
  );

  return api;
}

export async function apiSSR() {
  const api = await setupApiSSR();

  return api;
}
