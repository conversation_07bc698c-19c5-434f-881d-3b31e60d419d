import {
  Box,
  Flex,
  Grid,
  Grid<PERSON>tem,
  H<PERSON>tack,
  VStack,
  Image,
  Text,
} from "@chakra-ui/react";

export default function ConclusionPage() {
  return (
    <Flex
      w={"100%"}
      minH={"100vh"}
      style={{
        background: "rgb(35,34,34)",
      }}
    >
      <Flex flex={1} position={"relative"} overflow={"hidden"}>
        <Box
          position="absolute"
          top={0}
          left={0}
          transform="translateY(50%)"
          w={"100%"}
          h={"100%"}
          bgImg="url(/images/login/padraoBG.svg)"
          bgRepeat="no-repeat"
          bgPos="center bottom"
          bgSize="contain"
          clipPath="inset(0 0 50% 0)"
        />
        <Grid
          templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }}
          gap={1}
          w={"100%"}
          h="100vh"
          position={"relative"}
        >
          <GridItem>
            <VStack
              h={{ base: "100%", lg: "50%" }}
              w={"100%"}
              justifyContent={{ base: "center", lg: "space-between" }}
              alignItems="start"
              gap={10}
              px={{ base: 6, lg: 20 }}
            >
              <HStack
                w={"100%"}
                h={{ base: "auto", lg: "50%" }}
                justifyContent={"start"}
                alignItems="center"
                gap={10}
                pt={{ base: 20, "2xl": 0 }}
              >
                <Image
                  src="/images/logoBancoABC.svg"
                  alt="Banco ABC"
                  w={{base: "70px",lg: "100px"}}
                  h="auto"
                />
              </HStack>
              <VStack alignItems="start" mb={4}>
                <Text
                  fontSize={{ "2xl": 32, base: 26 }}
                  fontWeight="light"
                  textAlign={"start"}
                >
                  Agradecemos sua participação no assessment da Escola de
                  Excelência Comercial.
                </Text>
                <Text
                  fontSize={{ "2xl": 28, base: 24 }}
                  fontWeight="light"
                  color="rgb(131,102,58)"
                >
                  Em breve, o RH entrará em contato com o resultado.
                </Text>
              </VStack>
            </VStack>
          </GridItem>
          <GridItem display={{ base: "block", lg: "block" }}>
            <Box
              bgImage="url(/images/img-video-2.png)"
              w="100%"
              h="100vh"
              borderRadius={{base: 7, lg: "none"}}
              bgRepeat="no-repeat"
              bgSize={{base: "100% 50%", lg: "cover"}}
              position="relative"
              _after={{
                content: '""',
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: {
                  base: "none",
                  lg: "linear-gradient(to right, rgba(35,34,34,1) 0%, transparent 3%)",
                }
              }}
            />
          </GridItem>
        </Grid>
      </Flex>
    </Flex>
  );
}
