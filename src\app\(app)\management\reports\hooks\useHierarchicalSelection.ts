import { useState, useCallback } from "react";
import { Segment, UseHierarchicalSelectionReturn } from "../data/types";

export function useHierarchicalSelection(
  data: Segment[]
): UseHierarchicalSelectionReturn {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [allSelected, setAllSelected] = useState(false);

  // Get all possible IDs for hierarchy management
  const getAllChildIds = useCallback((segment: Segment): string[] => {
    const ids: string[] = [segment.secureId];
    segment.superintendencias.forEach((sup) => {
      ids.push(sup.secureId);
      sup.gerencias.forEach((ger) => {
        ids.push(ger.secureId);
        ger.cargos.forEach((cargo) => {
          ids.push(cargo.secureId);
        });
      });
    });
    return ids;
  }, []);

  const getAllIds = useCallback((): string[] => {
    const allIds: string[] = [];
    data.forEach((segment) => {
      allIds.push(...getAllChildIds(segment));
    });
    return allIds;
  }, [data, getAllChildIds]);

  // Handle hierarchical selection
  const handleItemChange = useCallback(
    (id: string, checked: boolean) => {
      const newSelected = new Set(selectedItems);

      if (checked) {
        newSelected.add(id);
        // Add all children
        data.forEach((segment) => {
          if (segment.secureId === id) {
            getAllChildIds(segment).forEach((childId) =>
              newSelected.add(childId)
            );
          } else {
            segment.superintendencias.forEach((sup) => {
              if (sup.secureId === id) {
                newSelected.add(sup.secureId);
                sup.gerencias.forEach((ger) => {
                  newSelected.add(ger.secureId);
                  ger.cargos.forEach((cargo) =>
                    newSelected.add(cargo.secureId)
                  );
                });
              } else {
                sup.gerencias.forEach((ger) => {
                  if (ger.secureId === id) {
                    newSelected.add(ger.secureId);
                    ger.cargos.forEach((cargo) =>
                      newSelected.add(cargo.secureId)
                    );
                  }
                });
              }
            });
          }
        });
      } else {
        newSelected.delete(id);
        // Remove all children
        data.forEach((segment) => {
          if (segment.secureId === id) {
            getAllChildIds(segment).forEach((childId) =>
              newSelected.delete(childId)
            );
          } else {
            segment.superintendencias.forEach((sup) => {
              if (sup.secureId === id) {
                newSelected.delete(sup.secureId);
                sup.gerencias.forEach((ger) => {
                  newSelected.delete(ger.secureId);
                  ger.cargos.forEach((cargo) =>
                    newSelected.delete(cargo.secureId)
                  );
                });
              } else {
                sup.gerencias.forEach((ger) => {
                  if (ger.secureId === id) {
                    newSelected.delete(ger.secureId);
                    ger.cargos.forEach((cargo) =>
                      newSelected.delete(cargo.secureId)
                    );
                  }
                });
              }
            });
          }
        });
      }

      setSelectedItems(newSelected);
      setAllSelected(false);
    },
    [selectedItems, data, getAllChildIds]
  );

  // Handle "TODOS" selection
  const handleAllChange = useCallback(
    (checked: boolean) => {
      setAllSelected(checked);
      if (checked) {
        setSelectedItems(new Set(getAllIds()));
      } else {
        setSelectedItems(new Set());
      }
    },
    [getAllIds]
  );

  return {
    selectedItems,
    allSelected,
    handleItemChange,
    handleAllChange,
  };
}
