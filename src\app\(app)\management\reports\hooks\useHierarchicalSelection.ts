import { useState, useCallback } from "react";
import { Segment, UseHierarchicalSelectionReturn } from "../data/types";

export function useHierarchicalSelection(
  data: Segment[]
): UseHierarchicalSelectionReturn {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [allSelected, setAllSelected] = useState(false);

  // Create a unique key for each item based on its full hierarchical path
  const createHierarchicalKey = useCallback((
    segmentId: string,
    superintendenciaId?: string,
    gerenciaId?: string,
    cargoId?: string
  ): string => {
    const parts = [segmentId];
    if (superintendenciaId) parts.push(superintendenciaId);
    if (gerenciaId) parts.push(gerenciaId);
    if (cargoId) parts.push(cargoId);
    return parts.join('|');
  }, []);

  // Get all possible hierarchical keys for a segment
  const getAllChildKeys = useCallback((segment: Segment): string[] => {
    const keys: string[] = [createHierarchicalKey(segment.secureId)];
    segment.superintendencias.forEach((sup) => {
      keys.push(createHierarchicalKey(segment.secureId, sup.secureId));
      sup.gerencias.forEach((ger) => {
        keys.push(createHierarchicalKey(segment.secureId, sup.secureId, ger.secureId));
        ger.cargos.forEach((cargo) => {
          keys.push(createHierarchicalKey(segment.secureId, sup.secureId, ger.secureId, cargo.secureId));
        });
      });
    });
    return keys;
  }, [createHierarchicalKey]);

  const getAllKeys = useCallback((): string[] => {
    const allKeys: string[] = [];
    data.forEach((segment) => {
      allKeys.push(...getAllChildKeys(segment));
    });
    return allKeys;
  }, [data, getAllChildKeys]);

  // Find the hierarchical context for a given secureId
  const findHierarchicalContext = useCallback((targetId: string) => {
    for (const segment of data) {
      if (segment.secureId === targetId) {
        return { segment, type: 'segment' as const };
      }
      for (const sup of segment.superintendencias) {
        if (sup.secureId === targetId) {
          return { segment, superintendencia: sup, type: 'superintendencia' as const };
        }
        for (const ger of sup.gerencias) {
          if (ger.secureId === targetId) {
            return { segment, superintendencia: sup, gerencia: ger, type: 'gerencia' as const };
          }
          for (const cargo of ger.cargos) {
            if (cargo.secureId === targetId) {
              return { segment, superintendencia: sup, gerencia: ger, cargo, type: 'cargo' as const };
            }
          }
        }
      }
    }
    return null;
  }, [data]);

  // Handle hierarchical selection with precise context-aware logic
  const handleItemChange = useCallback(
    (id: string, checked: boolean) => {
      const newSelected = new Set(selectedItems);
      const context = findHierarchicalContext(id);

      if (!context) return;

      if (checked) {
        // Add the selected item using hierarchical key
        const hierarchicalKey = createHierarchicalKey(
          context.segment.secureId,
          context.superintendencia?.secureId,
          context.gerencia?.secureId,
          context.cargo?.secureId
        );
        newSelected.add(hierarchicalKey);

        // Add all children within the same hierarchical branch
        if (context.type === 'segment') {
          // Select all children of this segment
          context.segment.superintendencias.forEach((sup) => {
            newSelected.add(createHierarchicalKey(context.segment.secureId, sup.secureId));
            sup.gerencias.forEach((ger) => {
              newSelected.add(createHierarchicalKey(context.segment.secureId, sup.secureId, ger.secureId));
              ger.cargos.forEach((cargo) => {
                newSelected.add(createHierarchicalKey(context.segment.secureId, sup.secureId, ger.secureId, cargo.secureId));
              });
            });
          });
        } else if (context.type === 'superintendencia') {
          // Select all children of this superintendencia
          context.superintendencia!.gerencias.forEach((ger) => {
            newSelected.add(createHierarchicalKey(context.segment.secureId, context.superintendencia!.secureId, ger.secureId));
            ger.cargos.forEach((cargo) => {
              newSelected.add(createHierarchicalKey(context.segment.secureId, context.superintendencia!.secureId, ger.secureId, cargo.secureId));
            });
          });
        } else if (context.type === 'gerencia') {
          // Select all children of this gerencia
          context.gerencia!.cargos.forEach((cargo) => {
            newSelected.add(createHierarchicalKey(context.segment.secureId, context.superintendencia!.secureId, context.gerencia!.secureId, cargo.secureId));
          });
        }
        // For 'cargo' type, no children to select
      } else {
        // Remove the selected item and all its children
        const hierarchicalKey = createHierarchicalKey(
          context.segment.secureId,
          context.superintendencia?.secureId,
          context.gerencia?.secureId,
          context.cargo?.secureId
        );
        newSelected.delete(hierarchicalKey);

        // Remove all children within the same hierarchical branch
        if (context.type === 'segment') {
          // Remove all children of this segment
          context.segment.superintendencias.forEach((sup) => {
            newSelected.delete(createHierarchicalKey(context.segment.secureId, sup.secureId));
            sup.gerencias.forEach((ger) => {
              newSelected.delete(createHierarchicalKey(context.segment.secureId, sup.secureId, ger.secureId));
              ger.cargos.forEach((cargo) => {
                newSelected.delete(createHierarchicalKey(context.segment.secureId, sup.secureId, ger.secureId, cargo.secureId));
              });
            });
          });
        } else if (context.type === 'superintendencia') {
          // Remove all children of this superintendencia
          context.superintendencia!.gerencias.forEach((ger) => {
            newSelected.delete(createHierarchicalKey(context.segment.secureId, context.superintendencia!.secureId, ger.secureId));
            ger.cargos.forEach((cargo) => {
              newSelected.delete(createHierarchicalKey(context.segment.secureId, context.superintendencia!.secureId, ger.secureId, cargo.secureId));
            });
          });
        } else if (context.type === 'gerencia') {
          // Remove all children of this gerencia
          context.gerencia!.cargos.forEach((cargo) => {
            newSelected.delete(createHierarchicalKey(context.segment.secureId, context.superintendencia!.secureId, context.gerencia!.secureId, cargo.secureId));
          });
        }
        // For 'cargo' type, no children to remove
      }

      setSelectedItems(newSelected);
      setAllSelected(false);
    },
    [selectedItems, data, findHierarchicalContext, createHierarchicalKey]
  );

  // Handle "TODOS" selection
  const handleAllChange = useCallback(
    (checked: boolean) => {
      setAllSelected(checked);
      if (checked) {
        setSelectedItems(new Set(getAllKeys()));
      } else {
        setSelectedItems(new Set());
      }
    },
    [getAllKeys]
  );

  // Helper function to check if an item is selected based on its hierarchical context
  const isItemSelected = useCallback((id: string): boolean => {
    const context = findHierarchicalContext(id);
    if (!context) return false;

    const hierarchicalKey = createHierarchicalKey(
      context.segment.secureId,
      context.superintendencia?.secureId,
      context.gerencia?.secureId,
      context.cargo?.secureId
    );
    return selectedItems.has(hierarchicalKey);
  }, [selectedItems, findHierarchicalContext, createHierarchicalKey]);

  return {
    selectedItems,
    allSelected,
    handleItemChange,
    handleAllChange,
    isItemSelected, // Export this helper for components to use
  };
}
