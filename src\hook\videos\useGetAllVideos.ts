import { api } from "@/services/api";
import { GetAllVideosDto } from "@/utils/types/DTO/videos/videos.dto";
import { useQuery } from "@tanstack/react-query";

type TGetAllVideos = {
  searchTerm?: string;
  page?: number;
  limit?: number;
};

async function getAllVideos(params: TGetAllVideos) {
  const { data } = await api.get<GetAllVideosDto>(`/management/video`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllVideos(params: TGetAllVideos) {
  return useQuery({
    queryKey: ["videos", params],
    queryFn: async () => await getAllVideos(params),
  });
}