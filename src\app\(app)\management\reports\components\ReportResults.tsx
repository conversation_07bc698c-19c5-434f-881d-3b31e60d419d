import { <PERSON>, VStack, H<PERSON>tack, Text, Image, Flex } from "@chakra-ui/react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cartes<PERSON>,
  <PERSON><PERSON>ist,
  ResponsiveContainer,
  <PERSON>ltip,
  XAxis,
} from "recharts";
import { ReportGenerationResponse } from "../data/types";

interface ReportResultsProps {
  reportData: ReportGenerationResponse;
}

export function ReportResults({ reportData }: ReportResultsProps) {
  console.log("reportData: ", reportData);
  // Transform API data to chart format
  const transformToChartData = (dimension: any) => {
    return [
      {
        name: "1",
        checkin: dimension.checkIn["proficiency-1"],
        checkout: dimension.checkOut
          ? dimension.checkOut["proficiency-1"]
          : undefined,
      },
      {
        name: "2",
        checkin: dimension.checkIn["proficiency-2"],
        checkout: dimension.checkOut
          ? dimension.checkOut["proficiency-2"]
          : undefined,
      },
      {
        name: "3",
        checkin: dimension.checkIn["proficiency-3"],
        checkout: dimension.checkOut
          ? dimension.checkOut["proficiency-3"]
          : undefined,
      },
      {
        name: "4",
        checkin: dimension.checkIn["proficiency-4"],
        checkout: dimension.checkOut
          ? dimension.checkOut["proficiency-4"]
          : undefined,
      },
      {
        name: "5",
        checkin: dimension.checkIn["proficiency-5"],
        checkout: dimension.checkOut
          ? dimension.checkOut["proficiency-5"]
          : undefined,
      },
    ];
  };

  // Helper function to check if checkout data exists
  const hasCheckoutData = (data: any) => {
    return data.checkOut && data.checkOut.count > 0;
  };

  // Helper function to check if the report has any checkout data
  const reportHasCheckoutData = () => {
    if (reportData) {
      // Check if checkout exists in the main averages
      if (
        reportData.averageCheckInCheckOut?.checkOut !== undefined &&
        reportData.averageCheckInCheckOut.checkOut !== null
      ) {
        return true;
      }
      // Check if any dimension has checkout data
      return (
        reportData.averageDimensions?.some((dimension) =>
          hasCheckoutData(dimension)
        ) || false
      );
    }
    return true; // Default to true for example data
  };

  if (reportData?.respondents.names.length === 1) {
    return (
      <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
        <Box
          position="absolute"
          top={0}
          right={0}
          transform={"scale(2.5)"}
          w={"10%"}
          h={"100%"}
          bgImg="url(/images/padraoBG-01.svg)"
          bgRepeat="no-repeat"
          bgPos="center left"
          bgSize="cover"
          clipPath="inset(0 0 0 0)"
        />
        <VStack w="100%" gap={6} align="stretch" p={6}>
          <HStack gap={8}>
            <Image
              src="/images/logoBancoABC.svg"
              alt="Banco ABC"
              w="100px"
              h="auto"
            />
            <HStack gap={4} w={"50%"}>
              <Box
                bgColor={"gray.300"}
                color={"black"}
                borderRadius={"lg"}
                p={3}
              >
                <Text color={"black"} fontSize={"md"}>
                  Nome: {reportData?.respondents.names[0]}
                </Text>
              </Box>
              <Box
                bgColor={"gray.300"}
                color={"black"}
                borderRadius={"lg"}
                p={3}
              >
                <Text color={"black"} fontSize={"md"}>
                  Check-in: Data
                </Text>
              </Box>
              {reportHasCheckoutData() && (
                <Box
                  bgColor={"gray.300"}
                  color={"black"}
                  borderRadius={"lg"}
                  p={3}
                >
                  <Text color={"black"} fontSize={"md"}>
                    Check-out: Data
                  </Text>
                </Box>
              )}
            </HStack>
          </HStack>
          <HStack gap={20} px={20} align="stretch">
            <VStack gap={8} py={8} justifyContent="space-around">
              <Box bg={"#a6864a"} px={10} py={6} borderRadius={"4xl"}>
                <Text color={"white"} fontSize={"lg"} textAlign={"center"}>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem
                  ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
                  dolor sit amet, consectetur adipiscing elit.Lorem ipsum dolor
                  sit amet, consectetur adipiscing elit.Lorem ipsum dolor sit
                  amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet,
                  consectetur adipiscing elit.Lorem ipsum dolor sit amet,
                  consectetur adipiscing elit
                </Text>
              </Box>
              <VStack gap={4} w={"50%"}>
                <HStack>
                  <Box
                    bgColor={"gray.300"}
                    color={"black"}
                    borderRadius={"lg"}
                    p={3}
                  >
                    <Text color={"black"} fontSize={"md"}>
                      Check-in- Proficiência Geral:{" "}
                    </Text>
                  </Box>
                  <Box
                    color={"white"}
                    fontSize={"md"}
                    border={"2px solid #a6864a"}
                    borderRadius={"lg"}
                    p={2}
                  >
                    <Text color={"white"} fontSize={"xl"}>
                      {reportData?.averageCheckInCheckOut?.checkIn}
                    </Text>
                  </Box>
                </HStack>
                {reportHasCheckoutData() && (
                  <HStack>
                    <Box
                      bgColor={"gray.300"}
                      color={"black"}
                      borderRadius={"lg"}
                      p={3}
                    >
                      <Text color={"black"} fontSize={"md"}>
                        Check-out- Proficiência Geral:{" "}
                      </Text>
                    </Box>
                    <Box
                      color={"white"}
                      fontSize={"md"}
                      border={"2px solid #a6864a"}
                      borderRadius={"lg"}
                      p={2}
                    >
                      <Text color={"white"} fontSize={"xl"}>
                        {reportData?.averageCheckInCheckOut?.checkOut}
                      </Text>
                    </Box>
                  </HStack>
                )}
              </VStack>
              <Text color={"white"} fontSize={"lg"} textAlign={"center"}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem
                ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
                dolor sit amet, consectetur adipiscing elit.Lorem ipsum dolor
                sit amet, consectetur adipiscing elit.Lorem ipsum dolor sit
                amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet,
                consectetur adipiscing elit.Lorem ipsum dolor sit amet,
                consectetur adipiscing elit.
              </Text>
            </VStack>
            <Image
              src="/images/mandala.png"
              alt="Mandala"
              w="800px"
              zIndex={1}
            />
          </HStack>

          {/* Charts for each dimension */}
          {reportData.averageDimensions.map((dimension, index) => {
            const hasCheckout = hasCheckoutData(dimension);
            return (
              <Box key={index} px={6}>
                <HStack
                  w="100%"
                  bg={dimension.bgColor}
                  borderRadius="4xl"
                  gap={0}
                  align="stretch"
                >
                  <Flex
                    flex={1.5}
                    color="white"
                    justify="center"
                    align="center"
                    direction="column"
                    gap={4}
                  >
                    <HStack>
                      <Text fontSize="lg">{dimension.name}:</Text>
                      <Box
                        border="1px solid white"
                        borderRadius="md"
                        px={1}
                        py={1}
                      >
                        <Text fontSize="lg">{dimension.checkIn.average}</Text>
                      </Box>
                      {hasCheckout && (
                        <Box
                          border="1px solid white"
                          borderRadius="md"
                          px={1}
                          py={1}
                        >
                          <Text fontSize="lg">
                            {dimension.checkOut?.average}
                          </Text>
                        </Box>
                      )}
                    </HStack>
                  </Flex>

                  <VStack
                    flex={3}
                    bg="#B2B2B2"
                    borderLeftRadius="4xl"
                    p={4}
                    gap={4}
                    h="200px"
                    position={"relative"}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={transformToChartData(dimension)}
                        margin={{ top: 25, right: 60 }}
                      >
                        <XAxis dataKey="name" stroke="black" tickLine={false} />
                        <Tooltip
                          cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                        />
                        <Bar
                          dataKey="checkin"
                          fill={dimension.barsColor}
                          barSize={hasCheckout ? 24 : 48}
                        >
                          <LabelList
                            dataKey="checkin"
                            position="top"
                            fill="black"
                            style={{ fontSize: "12px" }}
                          />
                        </Bar>
                        {hasCheckout && (
                          <Bar dataKey="checkout" fill="#00B0F0" barSize={24}>
                            <LabelList
                              dataKey="checkout"
                              position="top"
                              fill="black"
                              style={{ fontSize: "12px" }}
                            />
                          </Bar>
                        )}
                      </BarChart>
                    </ResponsiveContainer>
                  </VStack>
                </HStack>
              </Box>
            );
          })}
        </VStack>
      </Box>
    );
  }

  // Multiple Results View
  return (
    <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
      <VStack w="100%" gap={8} align="stretch" p={6}>
        <HStack gap={8}>
          <Image
            src="/images/logoBancoABC.svg"
            alt="Banco ABC"
            w="100px"
            h="auto"
          />
          <HStack gap={4} w={"50%"}>
            <Box
              bgColor={"gray.300"}
              color={"black"}
              borderRadius={"lg"}
              py={3}
              px={6}
            >
              <Text color={"black"} fontSize={"md"}>
                Check-in
              </Text>
              <Text color={"black"} fontSize={"md"}>
                Quantidade de elegíveis à responder:{" "}
                {reportData?.respondents.checkIn.quantityAvailable}
              </Text>
              <Text color={"black"} fontSize={"md"}>
                Quantidade de respondentes:{" "}
                {reportData?.respondents.checkIn.quantityAnswered}
              </Text>
            </Box>
            {reportHasCheckoutData() && (
              <Box
                bgColor={"gray.300"}
                color={"black"}
                borderRadius={"lg"}
                py={3}
                px={6}
              >
                <Text color={"black"} fontSize={"md"}>
                  Check-out
                </Text>
                <Text color={"black"} fontSize={"md"}>
                  Quantidade de elegíveis à responder:{" "}
                  {reportData?.respondents.checkOut.quantityAvailable}
                </Text>
                <Text color={"black"} fontSize={"md"}>
                  Quantidade de respondentes:{" "}
                  {reportData?.respondents.checkOut.quantityAnswered}
                </Text>
              </Box>
            )}
          </HStack>
        </HStack>
        <HStack gap={20} w={"80%"} alignSelf={"center"}>
          <Image src="/images/mandala.png" alt="Mandala" w="500px" />
          <Text fontSize={"3xl"}>
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam
            ullamcorper, felis sed ultricies faucibus, justo libero feugiat
            turpis, in tincidunt nisi nulla sed purus. Vestibulum auctor, nunc
            nec varius vehicula, sapien nunc hendrerit neque, at ultricies dolor
            nisi nec lorem. Cras euismod arcu sit amet diam libero."
          </Text>
        </HStack>

        {/* Charts for each dimension */}
        {reportData.averageDimensions.map((dimension, index) => {
          const hasCheckout = hasCheckoutData(dimension);
          return (
            <Box key={index} px={6}>
              <HStack
                w="100%"
                bg={dimension.bgColor}
                borderRadius="4xl"
                gap={0}
                align="stretch"
              >
                <Flex
                  flex={1.5}
                  color="white"
                  justify="center"
                  align="center"
                  direction="column"
                  gap={4}
                >
                  <HStack>
                    <Text fontSize="lg">{dimension.name}:</Text>
                    <Box
                      border="1px solid white"
                      borderRadius="md"
                      px={1}
                      py={1}
                    >
                      <Text fontSize="lg">
                        {(dimension.checkIn.average / 100).toFixed(2)}
                      </Text>
                    </Box>
                    {hasCheckout && (
                      <Box
                        border="1px solid white"
                        borderRadius="md"
                        px={1}
                        py={1}
                      >
                        <Text fontSize="lg">
                          {dimension.checkOut?.average
                            ? (dimension.checkOut.average / 100).toFixed(2)
                            : 0}
                        </Text>
                      </Box>
                    )}
                  </HStack>
                </Flex>

                <VStack
                  flex={3}
                  bg="#B2B2B2"
                  borderLeftRadius="4xl"
                  p={4}
                  gap={4}
                  h="200px"
                  position={"relative"}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={transformToChartData(dimension)}
                      margin={{ top: 25, right: 60 }}
                    >
                      <XAxis dataKey="name" stroke="black" tickLine={false} />
                      <Tooltip cursor={{ fill: "rgba(255, 255, 255, 0.1)" }} />
                      <Bar
                        dataKey="checkin"
                        fill={dimension.barsColor}
                        barSize={hasCheckout ? 24 : 48}
                      >
                        <LabelList
                          dataKey="checkin"
                          position="top"
                          fill="black"
                          style={{ fontSize: "12px" }}
                        />
                      </Bar>
                      {hasCheckout && (
                        <Bar dataKey="checkout" fill="#00B0F0" barSize={24}>
                          <LabelList
                            dataKey="checkout"
                            position="top"
                            fill="black"
                            style={{ fontSize: "12px" }}
                          />
                        </Bar>
                      )}
                    </BarChart>
                  </ResponsiveContainer>
                </VStack>
              </HStack>
            </Box>
          );
        })}
      </VStack>
    </Box>
  );
}
