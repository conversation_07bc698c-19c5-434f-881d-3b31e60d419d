import { api } from "@/services/api";
import { GetAllStructuresDto } from "@/utils/types/DTO/hierarchy/structures.dto";
import { useQuery } from "@tanstack/react-query";

type TGetAllStructures = {
  searchTerm?: string;
  page?: number;
  limit?: number;
};

async function getAllStructures(params: TGetAllStructures) {
  const { data } = await api.get<GetAllStructuresDto>(`/management/hierarchy-structure`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllStructures(params: TGetAllStructures) {
  return useQuery({
    queryKey: ["structures", params],
    queryFn: async () => await getAllStructures(params),
  });
}